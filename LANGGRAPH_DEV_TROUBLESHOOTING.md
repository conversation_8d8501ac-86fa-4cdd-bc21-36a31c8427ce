# LangGraph Dev Troubleshooting Guide

## 🚨 **Issue: ModuleNotFoundError: No module named 'starlette._exception_handler'**

This error occurs when running `langgraph dev` due to version incompatibilities between LangGraph CLI, Starlette, and FastAPI.

## 🔧 **Solution Steps**

### **Step 1: Run the Automated Fix Script**

```bash
python fix_langgraph_dev.py
```

This script will:
- ✅ Check Python version compatibility
- ✅ Uninstall conflicting packages
- ✅ Install dependencies in correct order
- ✅ Verify installation
- ✅ Test LangGraph dev command

### **Step 2: Manual Installation (if script fails)**

```bash
# 1. Clean up existing installations
pip uninstall fastapi starlette uvicorn langgraph-cli langgraph -y

# 2. Install core dependencies in order
pip install starlette==0.27.0
pip install fastapi==0.104.1
pip install 'uvicorn[standard]==0.24.0'

# 3. Install LangGraph dependencies
pip install 'langchain>=0.2.0'
pip install 'langchain-core>=0.2.0'
pip install 'langchain-openai>=0.1.0'
pip install 'langchain-community>=0.2.0'
pip install 'langgraph>=0.2.28'

# 4. Install LangGraph CLI last
pip install 'langgraph-cli>=0.1.39'

# 5. Install remaining dependencies
pip install -r requirements.txt
```

### **Step 3: Verify Installation**

```bash
# Test imports
python -c "
import starlette._exception_handler
import fastapi
import langgraph
print('✅ All imports successful')
"

# Test LangGraph CLI
langgraph dev --help
```

### **Step 4: Start LangGraph Dev**

```bash
# Ensure you're in the project directory
cd /path/to/Receptionist_Chatbot

# Start LangGraph Studio
langgraph dev
```

## 🎯 **Expected Results**

After successful fix:

1. **LangGraph Studio starts** without Starlette errors
2. **Server runs on** http://localhost:8123
3. **Project loads** showing both graphs:
   - `receptionist_chatbot`: Main conversation graph
   - `package_subgraph`: HIL-enabled package subgraph

## 📊 **Visualizing the Merged HIL Subgraph**

Once LangGraph Studio is running:

### **Main Graph Structure:**
```
classify_intent → [greeting|booking|info|package|unknown]_flow → END
```

### **Package Subgraph Structure (7 nodes):**
```
extract_slots → validate_extraction → check_completion
     ↓               ↓                    ↓
Input + Extract   Quality check    Completeness check
     ↑               ↓                    ↓
     └── human_feedback ← request_clarification   build_query
                                              ↓
                                         validate_query → final_answer
```

### **HIL Features Visible in Studio:**
- ✅ **Interrupt Points**: `human_feedback` node shows interrupt configuration
- ✅ **Conditional Routing**: See HIL decision logic in `validate_extraction`
- ✅ **State Transitions**: Monitor confidence scores and validation errors
- ✅ **Feedback Loops**: Visualize human-agent cycling

## 🐛 **Common Issues & Solutions**

### **Issue 1: "Graph not found" error**
**Solution:** Check `langgraph.json` configuration:
```json
{
  "dependencies": ["."],
  "graphs": {
    "receptionist_chatbot": "./app/services/conversation_graph.py:ConversationGraph",
    "package_subgraph": "./app/flows/package_flow.py:create_package_subgraph"
  },
  "env": ".env"
}
```

### **Issue 2: Import errors in Studio**
**Solution:** Ensure all dependencies are installed:
```bash
pip install -r requirements.txt
```

### **Issue 3: Environment variables not loaded**
**Solution:** Create `.env` file:
```env
OPENAI_API_KEY=your_openai_api_key_here
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
```

### **Issue 4: Conda environment conflicts**
**Solution:** Activate correct environment:
```bash
conda activate reception_bot
pip install -r requirements.txt
```

## 🔍 **Debugging HIL Functionality**

### **Test HIL Triggers:**

1. **Low Confidence Input:**
   ```
   Input: "I want something"
   Expected: HIL validation triggers
   ```

2. **Contradictory Input:**
   ```
   Input: "I want vaccine surgery"
   Expected: Contradiction detection triggers HIL
   ```

3. **Clear Input:**
   ```
   Input: "I want botox aesthetic treatment for adults for beauty"
   Expected: No HIL, direct processing
   ```

### **Monitor in Studio:**
- **State Panel**: Watch confidence scores update
- **Messages Panel**: See HIL clarification requests
- **Graph Panel**: Observe routing decisions

## 📞 **Support**

If issues persist:

1. **Check Python version**: Must be 3.8+
2. **Verify conda environment**: `conda list | grep langgraph`
3. **Check package versions**: `pip list | grep -E "(fastapi|starlette|langgraph)"`
4. **Restart terminal/IDE** after installation
5. **Clear Python cache**: `python -Bc "import py_compile; py_compile.compile('app/main.py')"`

## ✅ **Success Indicators**

You'll know everything is working when:

- ✅ `langgraph dev` starts without errors
- ✅ Studio opens at http://localhost:8123
- ✅ Both graphs load and visualize correctly
- ✅ HIL nodes show interrupt configuration
- ✅ State inspection shows all fields
- ✅ Test conversations trigger HIL appropriately

## 🎉 **Final Verification**

Run this test to confirm everything works:

```bash
# Start LangGraph Studio
langgraph dev

# In another terminal, test the API
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{"message": "I want something for beauty"}'
```

Expected response should show HIL clarification request with confidence scores and specific prompts for missing information.
