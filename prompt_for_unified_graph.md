# Task: Implement Unified Graph Visualization for LangGraph

## Background
Our chatbot uses LangGraph with a main conversation graph and a separate package subgraph. Currently, we can't visualize both in a single diagram using LangGraph's built-in utilities.

## Objective
Restructure the codebase to define a single unified graph that includes both the main conversation flow and the package subgraph nodes directly, allowing for complete visualization with LangGraph's native tools.

## Implementation Steps

1. Modify `app/services/conversation_graph.py`:
   - Update `_create_graph()` to add package subgraph nodes directly to the main graph
   - Add edges between package nodes and from package nodes to END
   - Add conditional routing for package flow decision points

2. Create adapter methods in the `ConversationGraph` class:
   - Add methods like `_package_await_input_node()` that convert between GraphState and MedicalPackageSubgraphState
   - Implement routing functions like `_should_continue_extraction()` and `_should_retry_query()`
   - Import necessary components from package_flow.py

3. Create a visualization script that uses LangGraph's built-in utilities to generate diagrams of the complete graph

## Expected Output
A single comprehensive graph visualization showing both the main conversation flow and all package subgraph nodes and their relationships in one diagram.

## Technical Requirements
- Maintain all existing functionality
- Use adapter pattern to convert between state formats
- Preserve all routing logic
- Generate visualizations in multiple formats (PNG, Mermaid source, ASCII)