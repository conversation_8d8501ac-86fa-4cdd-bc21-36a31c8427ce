---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	classify_intent(classify_intent)
	flow_greeting_start(flow_greeting_start)
	flow_booking_start(flow_booking_start)
	flow_info_start(flow_info_start)
	flow_package_start(flow_package_start)
	flow_unknown_start(flow_unknown_start)
	__end__([<p>__end__</p>]):::last
	__start__ --> classify_intent;
	classify_intent -. &nbsp;booking&nbsp; .-> flow_booking_start;
	classify_intent -. &nbsp;greeting&nbsp; .-> flow_greeting_start;
	classify_intent -. &nbsp;info&nbsp; .-> flow_info_start;
	classify_intent -. &nbsp;package&nbsp; .-> flow_package_start;
	classify_intent -. &nbsp;unknown&nbsp; .-> flow_unknown_start;
	flow_booking_start --> __end__;
	flow_greeting_start --> __end__;
	flow_info_start --> __end__;
	flow_package_start --> __end__;
	flow_unknown_start --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
