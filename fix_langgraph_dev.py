"""
Fix LangGraph Dev Starlette Dependency Issue
This script resolves the ModuleNotFoundError for starlette._exception_handler
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"   Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
        else:
            print(f"   ❌ Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False
    
    return True

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    print(f"   Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("   ✅ Python version is compatible")
        return True
    else:
        print("   ❌ Python 3.8+ required for LangGraph")
        return False

def fix_starlette_dependency():
    """Fix the Starlette dependency issue"""
    
    print("🚀 Fixing LangGraph Dev Starlette Dependency Issue")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Step 1: Uninstall potentially conflicting packages
    print("\n📦 Step 1: Cleaning up potentially conflicting packages...")
    cleanup_packages = [
        "fastapi",
        "starlette", 
        "uvicorn",
        "langgraph-cli",
        "langgraph"
    ]
    
    for package in cleanup_packages:
        run_command(f"pip uninstall {package} -y", f"Uninstalling {package}")
    
    # Step 2: Install core dependencies in correct order
    print("\n📦 Step 2: Installing core dependencies in correct order...")
    
    # Install Starlette first with specific version
    if not run_command("pip install starlette==0.27.0", "Installing Starlette 0.27.0"):
        return False
    
    # Install FastAPI with compatible version
    if not run_command("pip install fastapi==0.104.1", "Installing FastAPI 0.104.1"):
        return False
    
    # Install Uvicorn
    if not run_command("pip install 'uvicorn[standard]==0.24.0'", "Installing Uvicorn 0.24.0"):
        return False
    
    # Step 3: Install LangGraph dependencies
    print("\n📦 Step 3: Installing LangGraph dependencies...")
    
    langgraph_deps = [
        "langchain>=0.2.0",
        "langchain-core>=0.2.0", 
        "langchain-openai>=0.1.0",
        "langchain-community>=0.2.0",
        "langgraph>=0.2.28"
    ]
    
    for dep in langgraph_deps:
        if not run_command(f"pip install '{dep}'", f"Installing {dep}"):
            return False
    
    # Step 4: Install LangGraph CLI last
    print("\n📦 Step 4: Installing LangGraph CLI...")
    if not run_command("pip install 'langgraph-cli>=0.1.39'", "Installing LangGraph CLI"):
        return False
    
    # Step 5: Install remaining dependencies
    print("\n📦 Step 5: Installing remaining dependencies...")
    remaining_deps = [
        "pydantic==2.5.0",
        "python-dotenv==1.0.0",
        "openai>=1.3.0",
        "httpx==0.25.2",
        "python-multipart==0.0.6",
        "jinja2==3.1.2",
        "aiofiles==23.2.1",
        "anyio>=3.6.0",
        "sniffio>=1.3.0",
        "typing-extensions>=4.5.0"
    ]
    
    for dep in remaining_deps:
        run_command(f"pip install '{dep}'", f"Installing {dep}")
    
    return True

def verify_installation():
    """Verify that the installation is working"""
    
    print("\n🔍 Verifying installation...")
    
    # Test imports
    test_imports = [
        ("starlette._exception_handler", "Starlette exception handler"),
        ("fastapi", "FastAPI"),
        ("langgraph", "LangGraph"),
        ("langchain", "LangChain"),
        ("uvicorn", "Uvicorn")
    ]
    
    all_good = True
    for module, description in test_imports:
        try:
            __import__(module)
            print(f"   ✅ {description} import successful")
        except ImportError as e:
            print(f"   ❌ {description} import failed: {e}")
            all_good = False
    
    return all_good

def test_langgraph_dev():
    """Test if langgraph dev can start"""
    
    print("\n🧪 Testing LangGraph Dev startup...")
    
    # Check if langgraph.json exists
    if not Path("langgraph.json").exists():
        print("   ⚠️  langgraph.json not found, creating basic configuration...")
        create_langgraph_config()
    
    # Test langgraph dev command (dry run)
    print("   Testing langgraph dev command...")
    result = subprocess.run("langgraph dev --help", shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("   ✅ LangGraph CLI is working")
        return True
    else:
        print(f"   ❌ LangGraph CLI error: {result.stderr}")
        return False

def create_langgraph_config():
    """Create a basic langgraph.json configuration"""
    
    config = """{
  "dependencies": ["."],
  "graphs": {
    "receptionist_chatbot": "./app/services/conversation_graph.py:ConversationGraph"
  },
  "env": ".env"
}"""
    
    with open("langgraph.json", "w") as f:
        f.write(config)
    
    print("   ✅ Created langgraph.json configuration")

def main():
    """Main function to fix the Starlette dependency issue"""
    
    print("🤖 LangGraph Dev Starlette Dependency Fix")
    print("=" * 50)
    print("This script will resolve the ModuleNotFoundError for starlette._exception_handler")
    print()
    
    # Change to project directory
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    print(f"📁 Working directory: {project_dir.absolute()}")
    
    # Fix dependencies
    if not fix_starlette_dependency():
        print("\n❌ Failed to fix dependencies")
        return False
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed")
        return False
    
    # Test LangGraph dev
    if not test_langgraph_dev():
        print("\n❌ LangGraph dev test failed")
        return False
    
    print("\n🎉 SUCCESS! LangGraph Dev dependency issue fixed!")
    print("\n📋 Next steps:")
    print("   1. Run: langgraph dev")
    print("   2. Open browser to: http://localhost:8123")
    print("   3. Load the project and visualize the HIL subgraph")
    print("\n💡 If you still encounter issues:")
    print("   1. Restart your terminal/IDE")
    print("   2. Activate your conda environment: conda activate reception_bot")
    print("   3. Try running langgraph dev again")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
