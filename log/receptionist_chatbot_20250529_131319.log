2025-05-29 13:13:19 UTC+07:00 [INFO] app.utils.logging_config: Logging configured - Level: INFO, File: log/receptionist_chatbot_20250529_131319.log
2025-05-29 13:13:19 UTC+07:00 [INFO] app.utils.logging_config: Bangkok time: 2025-05-29 13:13:19 UTC+07:00
2025-05-29 13:13:19 UTC+07:00 [INFO] test_logger: Test log message with Bangkok timezone
2025-05-29 13:13:19 UTC+07:00 [WARNING] test_logger: This is a warning message
2025-05-29 13:13:19 UTC+07:00 [ERROR] test_logger: This is an error message
2025-05-29 13:13:23 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:24 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:24 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:25 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:25 UTC+07:00 [INFO] app.flows.package_flow: All 4 required fields captured, proceeding to build query
2025-05-29 13:13:30 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: LLM Response: { "query": "SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%beauty%' AND goal_tags LIKE '%confidence%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR 'any' = 'any') AND (target_group = 'adults' OR 'any' = 'any') LIMIT 10" }
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Generated SQL: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%beauty%' AND goal_tags LIKE '%confidence%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR 'any' = 'any') AND (target_group = 'adults' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Executing LLM-generated query: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%beauty%' AND goal_tags LIKE '%confidence%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR 'any' = 'any') AND (target_group = 'adults' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Query returned 10 results
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'description_short', 'target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'description_short', 'target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'description_short', 'target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'description_short', 'target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 13:13:30 UTC+07:00 [WARNING] app.flows.package_flow: Maximum iterations reached, proceeding to build query
2025-05-29 13:13:30 UTC+07:00 [WARNING] app.flows.package_flow: Maximum iterations reached, proceeding to build query
2025-05-29 13:13:31 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:31 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:31 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:35 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: LLM Response: { "query": "SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND ((description_short LIKE '%checkup%' OR description_short LIKE '%screening%' OR description_short LIKE '%treatment%') OR 'any' = 'any') AND (target_group LIKE '%' OR 'any' = 'any') LIMIT 10" }
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Generated SQL: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND ((description_short LIKE '%checkup%' OR description_short LIKE '%screening%' OR description_short LIKE '%treatment%') OR 'any' = 'any') AND (target_group LIKE '%' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Executing LLM-generated query: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND ((description_short LIKE '%checkup%' OR description_short LIKE '%screening%' OR description_short LIKE '%treatment%') OR 'any' = 'any') AND (target_group LIKE '%' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Query returned 10 results
2025-05-29 13:13:35 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: LLM Response: { "query": "SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%checkup screening treatment%' OR description_full LIKE '%checkup screening treatment%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10" }
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Generated SQL: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%checkup screening treatment%' OR description_full LIKE '%checkup screening treatment%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Executing LLM-generated query: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category LIKE '%checkup%' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%checkup screening treatment%' OR description_full LIKE '%checkup screening treatment%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10
2025-05-29 13:13:35 UTC+07:00 [INFO] app.flows.package_flow: Query returned 10 results
