2025-05-29 15:26:10 UTC+07:00 [INFO] app.utils.logging_config: Logging configured - Level: INFO, File: log/receptionist_chatbot_20250529_152610.log
2025-05-29 15:26:10 UTC+07:00 [INFO] app.utils.logging_config: Bangkok time: 2025-05-29 15:26:10 UTC+07:00
2025-05-29 15:26:10 UTC+07:00 [INFO] app.main: Starting Receptionist Chatbot API
2025-05-29 15:27:08 UTC+07:00 [INFO] app.main: Processing message: I'm looking for a botox package...
2025-05-29 15:27:08 UTC+07:00 [INFO] app.services.conversation_graph: Processing message for session None: I'm looking for a botox package...
2025-05-29 15:27:09 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 15:27:09 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'target_group'], continuing extraction
2025-05-29 15:27:09 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['goal_tags', 'target_group'], continuing extraction
2025-05-29 15:27:09 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 15:27:09 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 15:27:09 UTC+07:00 [INFO] app.flows.package_flow: Missing fields: ['target_group'], continuing extraction
2025-05-29 15:27:09 UTC+07:00 [WARNING] app.flows.package_flow: Maximum iterations reached, proceeding to build query
2025-05-29 15:27:15 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 15:27:15 UTC+07:00 [INFO] app.flows.package_flow: LLM Response: { "query": "SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR description_full LIKE '%botox%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10" }
2025-05-29 15:27:15 UTC+07:00 [INFO] app.flows.package_flow: Generated SQL: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR description_full LIKE '%botox%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10
2025-05-29 15:27:15 UTC+07:00 [INFO] app.flows.package_flow: Executing LLM-generated query: SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max FROM package_data WHERE (category = 'aesthetic' OR 'any' = 'any') AND (goal_tags LIKE '%health%' OR 'any' = 'any') AND (description_short LIKE '%botox%' OR description_full LIKE '%botox%' OR 'any' = 'any') AND (target_group LIKE '%any%' OR 'any' = 'any') LIMIT 10
2025-05-29 15:27:15 UTC+07:00 [INFO] app.flows.package_flow: Query returned 10 results
2025-05-29 15:27:15 UTC+07:00 [INFO] app.services.conversation_graph: Successfully processed message. Intent: package, Confidence: 0.95
2025-05-29 15:27:25 UTC+07:00 [INFO] app.main: Processing message: Woman age > 40...
2025-05-29 15:27:25 UTC+07:00 [INFO] app.services.conversation_graph: Processing message for session 0db7de21-f5dd-4ba8-9d53-289e6c059fb7: Woman age > 40...
2025-05-29 15:27:26 UTC+07:00 [INFO] httpx: HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-29 15:27:26 UTC+07:00 [INFO] app.services.conversation_graph: Successfully processed message. Intent: info, Confidence: 0.8
2025-05-30 02:06:51 UTC+07:00 [INFO] app.main: Shutting down Receptionist Chatbot API
