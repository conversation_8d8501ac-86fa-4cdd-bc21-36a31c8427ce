"""
<PERSON><PERSON><PERSON> to visualize state transitions of the receptionist chatbot including subgraphs
Exports visualization as PNG file
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from datetime import datetime

def create_state_transition_diagram():
    """Create a comprehensive state transition diagram"""
    
    # Create figure with high DPI for better quality
    fig, ax = plt.subplots(1, 1, figsize=(20, 14))
    ax.set_xlim(0, 20)
    ax.set_ylim(0, 14)
    ax.axis('off')
    
    # Color scheme
    colors = {
        'main_flow': '#2E86AB',      # Blue
        'intent': '#A23B72',         # Purple
        'subgraph': '#F18F01',       # Orange
        'decision': '#C73E1D',       # Red
        'terminal': '#4CAF50',       # Green
        'human_input': '#FF9800',    # Amber
        'llm': '#9C27B0'            # Deep Purple
    }
    
    # Title
    ax.text(10, 13.5, 'Receptionist Chatbot State Transition Diagram', 
            fontsize=20, fontweight='bold', ha='center')
    ax.text(10, 13, 'Dynamic Subgraph with LLM-Based SQL Generation', 
            fontsize=14, ha='center', style='italic')
    
    # Main conversation flow
    draw_main_flow(ax, colors)
    
    # Package subgraph (detailed)
    draw_package_subgraph(ax, colors)
    
    # Other flow boxes (simplified)
    draw_other_flows(ax, colors)
    
    # Legend
    draw_legend(ax, colors)
    
    # Add timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    ax.text(19.5, 0.5, f'Generated: {timestamp}', fontsize=8, ha='right', alpha=0.7)
    
    plt.tight_layout()
    return fig

def draw_main_flow(ax, colors):
    """Draw the main conversation flow"""
    
    # Start node
    start = FancyBboxPatch((1, 11), 2, 1, boxstyle="round,pad=0.1", 
                          facecolor=colors['terminal'], edgecolor='black', linewidth=2)
    ax.add_patch(start)
    ax.text(2, 11.5, 'START\n(User Input)', ha='center', va='center', fontweight='bold')
    
    # Intent Classification
    intent_box = FancyBboxPatch((5, 11), 3, 1, boxstyle="round,pad=0.1",
                               facecolor=colors['intent'], edgecolor='black', linewidth=2)
    ax.add_patch(intent_box)
    ax.text(6.5, 11.5, 'Intent Classification\n(OpenAI + Rules)', ha='center', va='center', 
            fontweight='bold', color='white')
    
    # Decision diamond
    decision = patches.RegularPolygon((10, 11.5), 4, radius=0.8, orientation=np.pi/4,
                                    facecolor=colors['decision'], edgecolor='black', linewidth=2)
    ax.add_patch(decision)
    ax.text(10, 11.5, 'Route\nIntent', ha='center', va='center', fontweight='bold', color='white')
    
    # Arrows for main flow
    draw_arrow(ax, 3, 11.5, 5, 11.5, 'black')  # Start to Intent
    draw_arrow(ax, 8, 11.5, 9.2, 11.5, 'black')  # Intent to Decision
    
    # Intent routing arrows
    intents = [
        ('greeting', 12.5, 9.5),
        ('booking', 12.5, 8),
        ('info', 12.5, 6.5),
        ('package', 12.5, 4.5),
        ('unknown', 12.5, 3)
    ]
    
    for intent, x, y in intents:
        # Draw arrow from decision to intent
        draw_arrow(ax, 10.5, 11.2, x-0.5, y+0.3, colors['main_flow'])
        ax.text(11.5, (11.2 + y + 0.3) / 2, intent, ha='center', va='center', 
                fontsize=9, bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))

def draw_package_subgraph(ax, colors):
    """Draw the detailed package subgraph"""
    
    # Subgraph container
    subgraph_box = FancyBboxPatch((12, 1.5), 7.5, 4, boxstyle="round,pad=0.2",
                                 facecolor='lightgray', edgecolor=colors['subgraph'], 
                                 linewidth=3, alpha=0.3)
    ax.add_patch(subgraph_box)
    ax.text(15.75, 5.2, 'Package Subgraph (Dynamic Loop)', ha='center', va='center',
            fontsize=12, fontweight='bold', color=colors['subgraph'])
    
    # Subgraph nodes
    nodes = [
        ('await_input', 13, 4.5, colors['human_input'], 'Human Input\n(Context-Aware)'),
        ('extract_slots', 15.5, 4.5, colors['main_flow'], 'Extract Slots\n(4 Fields)'),
        ('check_completion', 18, 4.5, colors['decision'], 'Check\nCompletion'),
        ('build_query', 15.5, 3, colors['llm'], 'Build Query\n(LLM SQL Gen)'),
        ('validate_query', 18, 3, colors['main_flow'], 'Validate Query\n(Async SQLite)'),
        ('final_answer', 15.5, 1.8, colors['terminal'], 'Final Answer\n(Results)')
    ]
    
    for name, x, y, color, label in nodes:
        if name == 'check_completion':
            # Diamond for decision
            node = patches.RegularPolygon((x, y), 4, radius=0.4, orientation=np.pi/4,
                                        facecolor=color, edgecolor='black', linewidth=1)
        else:
            # Rectangle for other nodes
            node = FancyBboxPatch((x-0.6, y-0.3), 1.2, 0.6, boxstyle="round,pad=0.05",
                                facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(node)
        
        text_color = 'white' if color in [colors['decision'], colors['llm'], colors['intent']] else 'black'
        ax.text(x, y, label, ha='center', va='center', fontsize=8, fontweight='bold', color=text_color)
    
    # Subgraph arrows
    # Main flow
    draw_arrow(ax, 13.6, 4.5, 14.9, 4.5, 'black')  # await_input -> extract_slots
    draw_arrow(ax, 16.1, 4.5, 17.6, 4.5, 'black')  # extract_slots -> check_completion
    
    # Loop back (missing fields)
    draw_curved_arrow(ax, 17.6, 4.2, 13.4, 4.2, 'red', 'continue\n(missing fields)')
    
    # Continue to build_query (all fields)
    draw_arrow(ax, 18, 4.1, 16.1, 3.3, 'green')
    ax.text(17.2, 3.6, 'all fields\ncaptured', ha='center', va='center', fontsize=7,
            bbox=dict(boxstyle="round,pad=0.1", facecolor='lightgreen', alpha=0.8))
    
    # build_query -> validate_query
    draw_arrow(ax, 16.1, 3, 17.4, 3, 'black')
    
    # validate_query -> final_answer
    draw_arrow(ax, 17.4, 2.7, 16.1, 2.1, 'black')
    
    # Retry loop for query validation
    draw_curved_arrow(ax, 18.4, 3.2, 18.4, 2.8, 'orange', 'retry')

def draw_other_flows(ax, colors):
    """Draw simplified boxes for other flows"""
    
    flows = [
        ('Greeting Flow', 12.5, 9.5, colors['main_flow']),
        ('Booking Flow', 12.5, 8, colors['main_flow']),
        ('Info Flow', 12.5, 6.5, colors['main_flow']),
        ('Unknown Flow', 12.5, 3, colors['main_flow'])
    ]
    
    for name, x, y, color in flows:
        flow_box = FancyBboxPatch((x, y-0.3), 2.5, 0.6, boxstyle="round,pad=0.1",
                                 facecolor=color, edgecolor='black', linewidth=1)
        ax.add_patch(flow_box)
        ax.text(x+1.25, y, name, ha='center', va='center', fontweight='bold', color='white')
        
        # Arrow to END
        draw_arrow(ax, x+2.5, y, 16.5, y, 'gray')
    
    # END node
    end_box = FancyBboxPatch((17, 7.5), 1.5, 2.5, boxstyle="round,pad=0.1",
                            facecolor=colors['terminal'], edgecolor='black', linewidth=2)
    ax.add_patch(end_box)
    ax.text(17.75, 8.75, 'END', ha='center', va='center', fontweight='bold', fontsize=12)

def draw_legend(ax, colors):
    """Draw legend explaining the colors and symbols"""
    
    legend_x = 0.5
    legend_y = 8
    
    ax.text(legend_x, legend_y + 1, 'Legend:', fontsize=12, fontweight='bold')
    
    legend_items = [
        ('Main Flow', colors['main_flow']),
        ('Intent Classification', colors['intent']),
        ('Decision Point', colors['decision']),
        ('Human Input', colors['human_input']),
        ('LLM Operation', colors['llm']),
        ('Terminal Node', colors['terminal'])
    ]
    
    for i, (label, color) in enumerate(legend_items):
        y_pos = legend_y - (i * 0.4)
        
        if label == 'Decision Point':
            # Diamond
            legend_shape = patches.RegularPolygon((legend_x + 0.3, y_pos), 4, radius=0.15, 
                                                orientation=np.pi/4, facecolor=color, edgecolor='black')
        else:
            # Rectangle
            legend_shape = FancyBboxPatch((legend_x + 0.15, y_pos - 0.1), 0.3, 0.2, 
                                        boxstyle="round,pad=0.02", facecolor=color, edgecolor='black')
        
        ax.add_patch(legend_shape)
        ax.text(legend_x + 0.7, y_pos, label, va='center', fontsize=10)

def draw_arrow(ax, x1, y1, x2, y2, color, style='-'):
    """Draw a simple arrow between two points"""
    ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                arrowprops=dict(arrowstyle='->', color=color, lw=1.5, linestyle=style))

def draw_curved_arrow(ax, x1, y1, x2, y2, color, label):
    """Draw a curved arrow with label"""
    # Calculate control point for curve
    mid_x = (x1 + x2) / 2
    mid_y = min(y1, y2) - 0.5
    
    ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                arrowprops=dict(arrowstyle='->', color=color, lw=1.5,
                              connectionstyle="arc3,rad=0.3"))
    
    # Add label
    ax.text(mid_x, mid_y, label, ha='center', va='center', fontsize=7,
            bbox=dict(boxstyle="round,pad=0.1", facecolor='white', alpha=0.8, edgecolor=color))

def add_detailed_annotations(ax):
    """Add detailed annotations about the system"""
    
    # 4 Required Fields annotation
    ax.text(15.5, 5.8, '4 Required Fields:', ha='center', va='center', fontsize=10, fontweight='bold')
    fields = ['• category', '• goal_tags', '• description_short', '• target_group']
    for i, field in enumerate(fields):
        ax.text(15.5, 5.6 - (i * 0.15), field, ha='center', va='center', fontsize=8)
    
    # LLM SQL Generation details
    ax.text(13.5, 2.5, 'LLM SQL Generation:', ha='left', va='center', fontsize=9, fontweight='bold')
    ax.text(13.5, 2.3, '• GPT-4 powered', ha='left', va='center', fontsize=8)
    ax.text(13.5, 2.1, '• JSON response format', ha='left', va='center', fontsize=8)
    ax.text(13.5, 1.9, '• Async non-blocking', ha='left', va='center', fontsize=8)

def save_visualization():
    """Create and save the visualization"""
    
    print("🎨 Creating State Transition Visualization...")
    
    # Create the diagram
    fig = create_state_transition_diagram()
    
    # Add detailed annotations
    add_detailed_annotations(fig.gca())
    
    # Save as PNG with high quality
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"receptionist_chatbot_state_transitions_{timestamp}.png"
    
    fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    
    print(f"✅ Visualization saved as: {filename}")
    print(f"📊 Image size: 20x14 inches at 300 DPI")
    print(f"🎯 Features included:")
    print("   • Main conversation flow")
    print("   • Dynamic package subgraph with looping")
    print("   • LLM-based SQL generation")
    print("   • Async SQLite operations")
    print("   • Intent classification routing")
    print("   • 4-field requirement system")
    print("   • Color-coded node types")
    print("   • Comprehensive legend")
    
    # Also save as PDF for vector graphics
    pdf_filename = f"receptionist_chatbot_state_transitions_{timestamp}.pdf"
    fig.savefig(pdf_filename, format='pdf', bbox_inches='tight', facecolor='white')
    print(f"✅ Vector version saved as: {pdf_filename}")
    
    plt.close(fig)
    
    return filename, pdf_filename

if __name__ == "__main__":
    # Check if matplotlib is available
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import numpy as np
        
        png_file, pdf_file = save_visualization()
        
        print(f"\n🎉 State transition visualization completed!")
        print(f"📁 Files created:")
        print(f"   • {png_file} (High-resolution PNG)")
        print(f"   • {pdf_file} (Vector PDF)")
        
    except ImportError as e:
        print(f"❌ Error: Missing required packages")
        print(f"Please install: pip install matplotlib numpy")
        print(f"Error details: {e}")
    except Exception as e:
        print(f"❌ Error creating visualization: {e}")
        import traceback
        traceback.print_exc()
