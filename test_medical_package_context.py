"""
Test script to verify all package-related content is about medical packages, not delivery
"""

import asyncio
from app.services.intent_classifier import IntentClassifier

async def test_medical_package_context():
    """Test that package intent only triggers for medical packages"""
    
    classifier = IntentClassifier()
    
    print("🧪 Testing Medical Package Context Classification...")
    print("=" * 60)
    
    # Test cases that SHOULD be classified as "package" (medical)
    medical_package_queries = [
        "I'm looking for a botox package",
        "Do you have vaccine packages?",
        "Show me health checkup packages",
        "I need aesthetic treatment packages",
        "What wellness packages are available?",
        "I want a medical package for surgery",
        "Show me vaccination packages",
        "I need package PK-VAC-HFMD-EV71-001"
    ]
    
    # Test cases that should NOT be classified as "package" (delivery/shipping)
    delivery_queries = [
        "I want to send a package",
        "Track my package delivery",
        "How much to ship a package?",
        "Package delivery to Bangkok",
        "My package is lost",
        "Courier service for packages",
        "Package tracking number",
        "Send parcel to New York"
    ]
    
    print("\n✅ Testing MEDICAL package queries (should be 'package' intent):")
    for query in medical_package_queries:
        intent, confidence = await classifier.classify_intent(query)
        status = "✅" if intent == "package" else "❌"
        print(f"   {status} '{query}' → {intent} ({confidence:.2f})")
    
    print("\n❌ Testing DELIVERY package queries (should NOT be 'package' intent):")
    for query in delivery_queries:
        intent, confidence = await classifier.classify_intent(query)
        status = "✅" if intent != "package" else "❌"
        expected = "info/unknown" if intent != "package" else "package (WRONG!)"
        print(f"   {status} '{query}' → {intent} ({confidence:.2f})")
    
    print("\n" + "=" * 60)
    print("🎉 Medical package context testing completed!")

if __name__ == "__main__":
    asyncio.run(test_medical_package_context())
