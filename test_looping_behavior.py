"""
Test script to verify the looping behavior of the dynamic package flow
"""

import asyncio
from app.flows.package_flow import (
    create_package_subgraph, 
    MedicalPackageSubgraphState,
    should_continue_extraction
)
from langchain_core.messages import HumanMessage

async def test_looping_behavior():
    """Test that the subgraph loops until all 4 fields are captured"""
    
    print("🔄 Testing Looping Behavior...")
    print("=" * 50)
    
    # Create the subgraph
    subgraph = create_package_subgraph()
    
    # Test case 1: Partial input that should trigger looping
    print("\n📝 Test 1: Partial input (should loop)")
    initial_state: MedicalPackageSubgraphState = {
        "messages": [HumanMessage(content="I want aesthetic treatments")],
        "user_input": "I want aesthetic treatments",
        "extracted_slots": {},
        "missing_fields": ["category", "goal_tags", "description_short", "target_group"],
        "query_results": [],
        "generated_sql": "",
        "response": "",
        "completed": False,
        "iteration_count": 0
    }
    
    try:
        # Run the subgraph - this should loop and ask for more information
        result = await subgraph.ainvoke(initial_state)
        
        print(f"   🔍 Final State:")
        print(f"   • Extracted Slots: {result.get('extracted_slots', {})}")
        print(f"   • Missing Fields: {result.get('missing_fields', [])}")
        print(f"   • Completed: {result.get('completed', False)}")
        print(f"   • Iteration Count: {result.get('iteration_count', 0)}")
        print(f"   • Response: {result.get('response', '')[:100]}...")
        
        # Check if it's asking for more information
        missing_fields = result.get('missing_fields', [])
        if missing_fields:
            print(f"   ✅ Correctly identified missing fields: {missing_fields}")
        else:
            print(f"   ❌ Should have missing fields but doesn't")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def test_conditional_routing():
    """Test the conditional routing logic"""
    
    print("\n🔀 Testing Conditional Routing...")
    print("=" * 40)
    
    test_cases = [
        {
            "name": "All fields present",
            "missing_fields": [],
            "iteration_count": 1,
            "expected": "build_query"
        },
        {
            "name": "Some fields missing",
            "missing_fields": ["goal_tags", "target_group"],
            "iteration_count": 2,
            "expected": "continue"
        },
        {
            "name": "Max iterations reached",
            "missing_fields": ["goal_tags"],
            "iteration_count": 6,
            "expected": "build_query"
        }
    ]
    
    for test in test_cases:
        print(f"\n📝 Test: {test['name']}")
        
        test_state: MedicalPackageSubgraphState = {
            "messages": [],
            "user_input": "",
            "extracted_slots": {},
            "missing_fields": test["missing_fields"],
            "query_results": [],
            "generated_sql": "",
            "response": "",
            "completed": False,
            "iteration_count": test["iteration_count"]
        }
        
        result = should_continue_extraction(test_state)
        
        if result == test["expected"]:
            print(f"   ✅ Correct routing: {result}")
        else:
            print(f"   ❌ Wrong routing: got {result}, expected {test['expected']}")

async def test_multi_turn_conversation():
    """Test a multi-turn conversation to capture all fields"""
    
    print("\n💬 Testing Multi-Turn Conversation...")
    print("=" * 45)
    
    # Create the subgraph
    subgraph = create_package_subgraph()
    
    # Conversation turns
    conversation_turns = [
        "I want aesthetic treatments",  # Should extract category
        "for beauty and confidence",    # Should extract goal_tags
        "botox injections",            # Should extract description_short
        "for adults"                   # Should extract target_group
    ]
    
    # Start with initial state
    state: MedicalPackageSubgraphState = {
        "messages": [],
        "user_input": "",
        "extracted_slots": {},
        "missing_fields": ["category", "goal_tags", "description_short", "target_group"],
        "query_results": [],
        "generated_sql": "",
        "response": "",
        "completed": False,
        "iteration_count": 0
    }
    
    for i, turn in enumerate(conversation_turns, 1):
        print(f"\n🗣️ Turn {i}: '{turn}'")
        
        # Add user message to state
        state["messages"].append(HumanMessage(content=turn))
        state["user_input"] = turn
        
        try:
            # Run one iteration of the subgraph
            result = await subgraph.ainvoke(state)
            
            print(f"   🔍 Extracted: {result.get('extracted_slots', {})}")
            print(f"   ❓ Missing: {result.get('missing_fields', [])}")
            print(f"   ✅ Completed: {result.get('completed', False)}")
            
            # Update state for next turn
            state = result
            
            # If completed, break
            if result.get('completed', False):
                print(f"   🎉 Conversation completed after {i} turns!")
                break
                
        except Exception as e:
            print(f"   ❌ Error in turn {i}: {e}")
            break

async def run_looping_tests():
    """Run all looping behavior tests"""
    print("🚀 Starting Looping Behavior Tests")
    print("=" * 50)
    
    await test_conditional_routing()
    await test_looping_behavior()
    await test_multi_turn_conversation()
    
    print("\n" + "=" * 50)
    print("🎉 ALL LOOPING TESTS COMPLETED!")

if __name__ == "__main__":
    asyncio.run(run_looping_tests())
