# Environment Configuration Template
# Copy this file to .env and fill in your values

# =============================================================================
# API Configuration
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# Environment Settings
# =============================================================================
ENVIRONMENT=development  # development, staging, production
LOG_LEVEL=INFO          # DEBUG, INFO, WARNING, ERROR, CRITICAL
DEBUG=false             # true for development, false for production

# =============================================================================
# Server Configuration
# =============================================================================
HOST=0.0.0.0
PORT=8000

# =============================================================================
# Database Configuration
# =============================================================================
DATABASE_PATH=data/package_data.db

# =============================================================================
# Intent Classification Settings
# =============================================================================
INTENT_CONFIDENCE_THRESHOLD=0.7

# =============================================================================
# LangGraph Configuration
# =============================================================================
LANGGRAPH_RECURSION_LIMIT=25

# =============================================================================
# Session Management
# =============================================================================
SESSION_TIMEOUT_MINUTES=30
MAX_SESSIONS=1000

# =============================================================================
# CORS Configuration
# =============================================================================
# For development: *
# For production: https://yourdomain.com,https://www.yourdomain.com
CORS_ORIGINS=*

# =============================================================================
# Production-Specific Settings (uncomment for production)
# =============================================================================
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=WARNING
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
# SESSION_TIMEOUT_MINUTES=60
# MAX_SESSIONS=10000
