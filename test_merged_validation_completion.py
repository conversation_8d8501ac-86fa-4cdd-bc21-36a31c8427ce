"""
Test script for the merged validate_and_check_completion node implementation
This verifies that the HIL functionality works correctly after merging validation and completion nodes
"""

import asyncio
from datetime import datetime
from pathlib import Path
from langchain_core.messages import HumanMessage, AIMessage

def test_merged_validation_completion():
    """Test the merged validation and completion node implementation"""
    
    print("🔧 Testing Merged Validation + Completion Implementation...")
    print("=" * 70)
    
    try:
        # Import the conversation graph
        from app.services.conversation_graph import ConversationGraph
        
        print("📊 Creating conversation graph with merged validation+completion node...")
        
        # Create the conversation graph
        conversation_graph = ConversationGraph()
        
        # Get graph information
        graph_info = conversation_graph.graph.get_graph()
        package_subgraph_info = conversation_graph.package_subgraph.get_graph()
        
        print(f"✅ Graph created successfully!")
        print(f"   • Main Graph Nodes: {len(graph_info.nodes)}")
        print(f"   • Package Subgraph Nodes: {len(package_subgraph_info.nodes)}")
        
        # Check that old nodes are NOT in the subgraph
        subgraph_nodes = list(package_subgraph_info.nodes.keys())
        print(f"\n📋 Package Subgraph Nodes (should be 6 total):")
        for node_name in sorted(subgraph_nodes):
            if node_name == "validate_and_check_completion":
                node_type = "MERGED NODE (Validation + Completion)"
            elif node_name == "extract_slots":
                node_type = "MERGED NODE (Input + Extract)"
            elif node_name in ["request_clarification", "human_feedback"]:
                node_type = "HIL Node"
            elif node_name in ["__start__", "__end__"]:
                node_type = "System"
            else:
                node_type = "Core"
            print(f"   • {node_name} ({node_type})")
        
        # Verify old nodes are NOT present
        old_nodes = ["validate_extraction", "check_completion"]
        missing_old_nodes = [node for node in old_nodes if node not in subgraph_nodes]
        present_old_nodes = [node for node in old_nodes if node in subgraph_nodes]
        
        if len(missing_old_nodes) == len(old_nodes):
            print(f"\n✅ SUCCESS: All old nodes successfully merged")
            print(f"   • validate_extraction: ❌ (merged)")
            print(f"   • check_completion: ❌ (merged)")
            print(f"   • validate_and_check_completion: ✅ (new merged node)")
        else:
            print(f"\n❌ ERROR: Some old nodes still exist: {present_old_nodes}")
            return False
        
        # Check node count
        expected_nodes = 6  # extract_slots, validate_and_check_completion, request_clarification, human_feedback, build_query, validate_query, final_answer
        actual_nodes = len([n for n in subgraph_nodes if not n.startswith("__")])
        
        print(f"\n🔢 Node Count Analysis:")
        print(f"   • Expected nodes: {expected_nodes}")
        print(f"   • Actual nodes: {actual_nodes}")
        if actual_nodes == expected_nodes:
            print(f"   ✅ Correct node count after merging")
        else:
            print(f"   ❌ Incorrect node count")
        
        # Check entry point
        entry_point = None
        for edge in package_subgraph_info.edges:
            if str(edge).startswith("__start__"):
                entry_point = str(edge).split(" -> ")[1] if " -> " in str(edge) else None
                break
        
        print(f"\n🎯 Entry Point Analysis:")
        print(f"   • Entry point: {entry_point}")
        if entry_point == "extract_slots":
            print(f"   ✅ Correct entry point (extract_slots)")
        else:
            print(f"   ❌ Incorrect entry point (should be extract_slots)")
        
        # Check merged node routing
        print(f"\n🔄 Merged Node Routing Analysis:")
        
        # Check extract_slots -> validate_and_check_completion
        extract_to_validate = any("extract_slots" in str(edge) and "validate_and_check_completion" in str(edge) for edge in package_subgraph_info.edges)
        print(f"   • extract_slots → validate_and_check_completion: {'✅' if extract_to_validate else '❌'}")
        
        # Check validate_and_check_completion has 3 possible routes
        validation_routes = [str(edge) for edge in package_subgraph_info.edges if "validate_and_check_completion" in str(edge)]
        print(f"   • validate_and_check_completion routing options: {len(validation_routes)}")
        
        # Check HIL feedback loop
        feedback_loop = any("human_feedback" in str(edge) and "extract_slots" in str(edge) for edge in package_subgraph_info.edges)
        print(f"   • human_feedback → extract_slots loop: {'✅' if feedback_loop else '❌'}")
        
        # Create output directory
        output_dir = Path("merged_validation_completion_test")
        output_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate visualizations
        print(f"\n🎨 Generating visualizations...")
        
        try:
            # Generate package subgraph PNG
            package_mermaid_png = conversation_graph.package_subgraph.get_graph().draw_mermaid_png()
            package_png_path = output_dir / f"merged_validation_completion_{timestamp}.png"
            
            with open(package_png_path, "wb") as f:
                f.write(package_mermaid_png)
            
            print(f"   ✅ Package subgraph PNG saved: {package_png_path}")
            
        except Exception as e:
            print(f"   ❌ Error creating package subgraph PNG: {e}")
        
        try:
            # Generate Mermaid source for package subgraph
            package_mermaid_source = conversation_graph.package_subgraph.get_graph().draw_mermaid()
            package_mermaid_path = output_dir / f"merged_validation_completion_{timestamp}.mmd"
            
            with open(package_mermaid_path, "w") as f:
                f.write(package_mermaid_source)
            
            print(f"   ✅ Package subgraph Mermaid saved: {package_mermaid_path}")
            
        except Exception as e:
            print(f"   ❌ Error creating package subgraph Mermaid: {e}")
        
        print(f"\n🎉 Merged Validation+Completion Test Complete!")
        print(f"📁 Output directory: {output_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing merged implementation: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_merged_routing_logic():
    """Test that the merged node handles all routing scenarios correctly"""
    
    print("\n" + "=" * 70)
    print("🗣️ Testing Merged Node Routing Logic...")
    print("=" * 70)
    
    try:
        from app.flows.package_flow import should_request_human_feedback_or_continue, MedicalPackageSubgraphState
        
        # Test scenarios for the merged routing function
        test_scenarios = [
            {
                "name": "Validation Issues (should request feedback)",
                "state": {
                    "clarification_needed": ["category (confidence: 0.4)"],
                    "validation_errors": [],
                    "missing_fields": [],
                    "iteration_count": 1
                },
                "expected": "request_feedback"
            },
            {
                "name": "Contradiction Errors (should request feedback)",
                "state": {
                    "clarification_needed": [],
                    "validation_errors": ["Contradiction: vaccine category with surgery description"],
                    "missing_fields": [],
                    "iteration_count": 1
                },
                "expected": "request_feedback"
            },
            {
                "name": "Missing Fields (should continue)",
                "state": {
                    "clarification_needed": [],
                    "validation_errors": [],
                    "missing_fields": ["category", "goal_tags"],
                    "iteration_count": 2
                },
                "expected": "continue"
            },
            {
                "name": "All Complete (should build query)",
                "state": {
                    "clarification_needed": [],
                    "validation_errors": [],
                    "missing_fields": [],
                    "iteration_count": 3
                },
                "expected": "build_query"
            },
            {
                "name": "Max Iterations (should build query anyway)",
                "state": {
                    "clarification_needed": [],
                    "validation_errors": [],
                    "missing_fields": ["category"],
                    "iteration_count": 6
                },
                "expected": "build_query"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🧪 Test Scenario {i}: {scenario['name']}")
            
            # Create test state
            test_state: MedicalPackageSubgraphState = {
                "messages": [],
                "user_input": "",
                "extracted_slots": {},
                "missing_fields": scenario["state"]["missing_fields"],
                "query_results": [],
                "generated_sql": "",
                "response": "",
                "completed": False,
                "iteration_count": scenario["state"]["iteration_count"],
                # HIL fields
                "awaiting_human_input": False,
                "human_feedback_requested": False,
                "feedback_context": "",
                "clarification_needed": scenario["state"]["clarification_needed"],
                "confidence_scores": {},
                "validation_errors": scenario["state"]["validation_errors"]
            }
            
            # Test the routing function
            result = should_request_human_feedback_or_continue(test_state)
            
            print(f"   Expected: {scenario['expected']}")
            print(f"   Actual: {result}")
            
            if result == scenario['expected']:
                print(f"   ✅ Test passed!")
            else:
                print(f"   ❌ Test failed!")
                return False
        
        print(f"\n✅ All routing logic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing routing logic: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Merged Validation+Completion Tests...")
    
    # Test the merged subgraph structure
    structure_success = test_merged_validation_completion()
    
    # Test the merged routing logic
    routing_success = asyncio.run(test_merged_routing_logic())
    
    if structure_success and routing_success:
        print(f"\n✅ All merged validation+completion tests passed!")
        print(f"💡 Key achievements:")
        print(f"   ✅ validate_extraction + check_completion merged into validate_and_check_completion")
        print(f"   ✅ Package subgraph reduced from 7 to 6 nodes")
        print(f"   ✅ All HIL functionality preserved")
        print(f"   ✅ Routing logic handles all scenarios correctly")
        print(f"   ✅ Three-way routing: request_feedback | continue | build_query")
        print(f"   ✅ Entry point and edges updated properly")
        print(f"\n🎯 New Architecture:")
        print(f"   extract_slots → validate_and_check_completion → [request_clarification|extract_slots|build_query]")
    else:
        print(f"\n❌ Some tests failed!")
        print(f"   Structure: {'✅' if structure_success else '❌'}")
        print(f"   Routing: {'✅' if routing_success else '❌'}")
