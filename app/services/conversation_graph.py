from typing import Dict, Any, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
import uuid
import logging
from datetime import datetime

from app.models.schemas import ConversationState
from app.services.intent_classifier import IntentClassifier
from app.flows.greeting_flow import flow_greeting_start
from app.flows.booking_flow import flow_booking_start
from app.flows.info_flow import flow_info_start
from app.flows.package_flow import (
    MedicalPackageSubgraphState,
    await_input_node,
    extract_slots_node,
    validate_extraction_node,
    request_clarification_node,
    human_feedback_node,
    check_completion_node,
    build_query_node,
    validate_query_node,
    final_answer_node,
    should_continue_extraction,
    should_retry_query,
    should_request_human_feedback
)
from app.flows.unknown_flow import flow_unknown_start
from app.utils.logging_config import get_logger

# Configure logger with Bangkok timezone
logger = get_logger(__name__)

class GraphState(TypedDict):
    """Unified state for the conversation graph that supports both main flow and package subgraph"""
    messages: Annotated[list[BaseMessage], add_messages]
    session_id: str
    current_intent: str
    confidence: float
    conversation_state: Dict[str, Any]  # Simplified for Studio compatibility
    response: str
    completed: bool

    # Package subgraph specific fields (optional for main flow)
    user_input: str
    extracted_slots: Dict[str, Any]
    missing_fields: list[str]
    query_results: list[Dict[str, Any]]
    generated_sql: str
    iteration_count: int

    # Human-in-the-loop specific fields
    awaiting_human_input: bool
    human_feedback_requested: bool
    feedback_context: str
    clarification_needed: list[str]
    confidence_scores: Dict[str, float]
    validation_errors: list[str]

class ConversationGraph:
    def __init__(self):
        self.intent_classifier = IntentClassifier()
        self.package_subgraph = self._create_package_subgraph()
        self.graph = self._create_graph()

    def _get_or_create_conversation_state(self, state: GraphState) -> ConversationState:
        """Helper method to get or create conversation state"""
        conv_state_dict = state.get("conversation_state", {})
        if not conv_state_dict:
            # Create default conversation state if not present
            conv_state_dict = {
                "session_id": state.get("session_id", "default"),
                "current_intent": None,
                "current_flow": None,
                "collected_data": {},
                "conversation_history": [],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
        return ConversationState(**conv_state_dict)

    def _create_package_subgraph(self) -> StateGraph:
        """Create the package subgraph as a compiled subgraph"""

        # Define the subgraph with GraphState (unified state)
        subgraph_builder = StateGraph(GraphState)

        # Add nodes with state adapters including HIL nodes
        subgraph_builder.add_node("await_input", self._await_input_adapter)
        subgraph_builder.add_node("extract_slots", self._extract_slots_adapter)
        subgraph_builder.add_node("validate_extraction", self._validate_extraction_adapter)
        subgraph_builder.add_node("request_clarification", self._request_clarification_adapter)
        subgraph_builder.add_node("human_feedback", self._human_feedback_adapter)
        subgraph_builder.add_node("check_completion", self._check_completion_adapter)
        subgraph_builder.add_node("build_query", self._build_query_adapter)
        subgraph_builder.add_node("validate_query", self._validate_query_adapter)
        subgraph_builder.add_node("final_answer", self._final_answer_adapter)

        # Set entry point
        subgraph_builder.set_entry_point("await_input")

        # Add edges with HIL conditional routing
        subgraph_builder.add_edge("await_input", "extract_slots")
        subgraph_builder.add_edge("extract_slots", "validate_extraction")

        # Conditional edge: check if extraction needs human validation
        subgraph_builder.add_conditional_edges(
            "validate_extraction",
            self._should_request_human_feedback_adapter,
            {
                "request_feedback": "request_clarification",
                "proceed": "check_completion"
            }
        )

        # HIL feedback flow
        subgraph_builder.add_edge("request_clarification", "human_feedback")
        subgraph_builder.add_edge("human_feedback", "extract_slots")  # Loop back to re-extract with new info

        # Conditional edge: if all fields captured, go to build_query, else loop back to await_input
        subgraph_builder.add_conditional_edges(
            "check_completion",
            self._should_continue_extraction_adapter,
            {
                "continue": "await_input",
                "build_query": "build_query"
            }
        )

        subgraph_builder.add_edge("build_query", "validate_query")
        subgraph_builder.add_conditional_edges(
            "validate_query",
            self._should_retry_query_adapter,
            {
                "retry": "build_query",
                "proceed": "final_answer"
            }
        )
        subgraph_builder.add_edge("final_answer", END)

        # Compile with interrupt support for HIL
        return subgraph_builder.compile(interrupt_before=["human_feedback"])

    def _convert_to_package_state(self, state: GraphState) -> MedicalPackageSubgraphState:
        """Convert GraphState to MedicalPackageSubgraphState"""
        return {
            "messages": state["messages"],
            "user_input": state.get("user_input", ""),
            "extracted_slots": state.get("extracted_slots", {}),
            "missing_fields": state.get("missing_fields", []),
            "query_results": state.get("query_results", []),
            "generated_sql": state.get("generated_sql", ""),
            "response": state.get("response", ""),
            "completed": state.get("completed", False),
            "iteration_count": state.get("iteration_count", 0),
            # HIL fields
            "awaiting_human_input": state.get("awaiting_human_input", False),
            "human_feedback_requested": state.get("human_feedback_requested", False),
            "feedback_context": state.get("feedback_context", ""),
            "clarification_needed": state.get("clarification_needed", []),
            "confidence_scores": state.get("confidence_scores", {}),
            "validation_errors": state.get("validation_errors", [])
        }

    def _update_from_package_state(self, state: GraphState, package_result: MedicalPackageSubgraphState) -> GraphState:
        """Update GraphState from MedicalPackageSubgraphState result"""
        state["messages"] = package_result["messages"]
        state["user_input"] = package_result["user_input"]
        state["extracted_slots"] = package_result["extracted_slots"]
        state["missing_fields"] = package_result["missing_fields"]
        state["query_results"] = package_result["query_results"]
        state["generated_sql"] = package_result["generated_sql"]
        state["response"] = package_result["response"]
        state["completed"] = package_result["completed"]
        state["iteration_count"] = package_result["iteration_count"]
        # HIL fields
        state["awaiting_human_input"] = package_result["awaiting_human_input"]
        state["human_feedback_requested"] = package_result["human_feedback_requested"]
        state["feedback_context"] = package_result["feedback_context"]
        state["clarification_needed"] = package_result["clarification_needed"]
        state["confidence_scores"] = package_result["confidence_scores"]
        state["validation_errors"] = package_result["validation_errors"]
        return state

    # State adapter methods for package subgraph nodes

    async def _await_input_adapter(self, state: GraphState) -> GraphState:
        """Adapter for await_input_node"""
        # Initialize package-specific fields if not present
        if not state.get("user_input"):
            state["user_input"] = state["messages"][-1].content if state["messages"] else ""
        if not state.get("extracted_slots"):
            state["extracted_slots"] = {}
        if not state.get("missing_fields"):
            state["missing_fields"] = ["category", "goal_tags", "description_short", "target_group"]
        if not state.get("iteration_count"):
            state["iteration_count"] = 0
        if not state.get("query_results"):
            state["query_results"] = []
        if not state.get("generated_sql"):
            state["generated_sql"] = ""

        package_state = self._convert_to_package_state(state)
        result = await await_input_node(package_state)
        return self._update_from_package_state(state, result)

    async def _extract_slots_adapter(self, state: GraphState) -> GraphState:
        """Adapter for extract_slots_node"""
        package_state = self._convert_to_package_state(state)
        result = await extract_slots_node(package_state)
        return self._update_from_package_state(state, result)

    async def _check_completion_adapter(self, state: GraphState) -> GraphState:
        """Adapter for check_completion_node"""
        package_state = self._convert_to_package_state(state)
        result = await check_completion_node(package_state)
        return self._update_from_package_state(state, result)

    def _should_continue_extraction_adapter(self, state: GraphState) -> str:
        """Adapter for should_continue_extraction"""
        package_state = self._convert_to_package_state(state)
        return should_continue_extraction(package_state)

    async def _build_query_adapter(self, state: GraphState) -> GraphState:
        """Adapter for build_query_node"""
        package_state = self._convert_to_package_state(state)
        result = await build_query_node(package_state)
        return self._update_from_package_state(state, result)

    async def _validate_query_adapter(self, state: GraphState) -> GraphState:
        """Adapter for validate_query_node"""
        package_state = self._convert_to_package_state(state)
        result = await validate_query_node(package_state)
        return self._update_from_package_state(state, result)

    def _should_retry_query_adapter(self, state: GraphState) -> str:
        """Adapter for should_retry_query"""
        package_state = self._convert_to_package_state(state)
        return should_retry_query(package_state)

    async def _final_answer_adapter(self, state: GraphState) -> GraphState:
        """Adapter for final_answer_node"""
        package_state = self._convert_to_package_state(state)
        result = await final_answer_node(package_state)
        return self._update_from_package_state(state, result)

    # HIL State adapter methods

    async def _validate_extraction_adapter(self, state: GraphState) -> GraphState:
        """Adapter for validate_extraction_node"""
        package_state = self._convert_to_package_state(state)
        result = await validate_extraction_node(package_state)
        return self._update_from_package_state(state, result)

    async def _request_clarification_adapter(self, state: GraphState) -> GraphState:
        """Adapter for request_clarification_node"""
        package_state = self._convert_to_package_state(state)
        result = await request_clarification_node(package_state)
        return self._update_from_package_state(state, result)

    async def _human_feedback_adapter(self, state: GraphState) -> GraphState:
        """Adapter for human_feedback_node"""
        package_state = self._convert_to_package_state(state)
        result = await human_feedback_node(package_state)
        return self._update_from_package_state(state, result)

    def _should_request_human_feedback_adapter(self, state: GraphState) -> str:
        """Adapter for should_request_human_feedback"""
        package_state = self._convert_to_package_state(state)
        return should_request_human_feedback(package_state)

    def _create_graph(self) -> StateGraph:
        """Create the main conversation graph"""

        # Define the graph
        workflow = StateGraph(GraphState)

        # Add nodes
        workflow.add_node("classify_intent", self._classify_intent_node)
        workflow.add_node("flow_greeting_start", self._greeting_flow_node)
        workflow.add_node("flow_booking_start", self._booking_flow_node)
        workflow.add_node("flow_info_start", self._info_flow_node)
        workflow.add_node("flow_package_start", self.package_subgraph)  # Use compiled subgraph
        workflow.add_node("flow_unknown_start", self._unknown_flow_node)

        # Set entry point
        workflow.set_entry_point("classify_intent")

        # Add conditional edges from intent classification
        workflow.add_conditional_edges(
            "classify_intent",
            self._route_intent,
            {
                "greeting": "flow_greeting_start",
                "booking": "flow_booking_start",
                "info": "flow_info_start",
                "package": "flow_package_start",
                "unknown": "flow_unknown_start"
            }
        )

        # Add edges to END for all flow nodes
        workflow.add_edge("flow_greeting_start", END)
        workflow.add_edge("flow_booking_start", END)
        workflow.add_edge("flow_info_start", END)
        workflow.add_edge("flow_package_start", END)
        workflow.add_edge("flow_unknown_start", END)

        return workflow.compile()

    async def _classify_intent_node(self, state: GraphState) -> GraphState:
        """Node to classify user intent"""
        # Get the latest message
        latest_message = state["messages"][-1].content if state["messages"] else ""

        # Classify intent
        intent, confidence = await self.intent_classifier.classify_intent(latest_message)

        # Update state
        state["current_intent"] = intent
        state["confidence"] = confidence

        return state

    def _route_intent(self, state: GraphState) -> str:
        """Route to appropriate flow based on classified intent"""
        return state["current_intent"]

    async def _greeting_flow_node(self, state: GraphState) -> GraphState:
        """Handle greeting flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_greeting_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].model_dump()

        return state

    async def _booking_flow_node(self, state: GraphState) -> GraphState:
        """Handle booking flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_booking_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].model_dump()

        return state

    async def _info_flow_node(self, state: GraphState) -> GraphState:
        """Handle info flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_info_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].model_dump()

        return state



    async def _unknown_flow_node(self, state: GraphState) -> GraphState:
        """Handle unknown flow"""
        message = state["messages"][-1].content if state["messages"] else ""
        conv_state = self._get_or_create_conversation_state(state)

        result = await flow_unknown_start(conv_state, message)

        # Add AI response to messages
        from langchain_core.messages import AIMessage
        state["messages"].append(AIMessage(content=result["response"]))

        state["response"] = result["response"]
        state["completed"] = result["completed"]
        state["conversation_state"] = result["state"].model_dump()

        return state

# Global conversation states storage (in production, use a database)
conversation_states: Dict[str, ConversationState] = {}

async def process_message(message: str, session_id: str = None) -> Dict[str, Any]:
    """Process a user message through the conversation graph"""

    try:
        logger.info(f"Processing message for session {session_id}: {message[:50]}...")

        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        # Get or create conversation state
        if session_id not in conversation_states:
            conversation_states[session_id] = ConversationState(
                session_id=session_id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

        conv_state = conversation_states[session_id]
        conv_state.updated_at = datetime.now()

        # Create graph instance
        graph = ConversationGraph()

        # Prepare initial state
        from langchain_core.messages import HumanMessage
        initial_state: GraphState = {
            "messages": [HumanMessage(content=message)],
            "session_id": session_id,
            "current_intent": "",
            "confidence": 0.0,
            "conversation_state": conv_state.model_dump(),  # Convert to dict for Studio compatibility
            "response": "",
            "completed": False,
            # Package subgraph fields (initialized as empty)
            "user_input": "",
            "extracted_slots": {},
            "missing_fields": [],
            "query_results": [],
            "generated_sql": "",
            "iteration_count": 0,
            # HIL fields (initialized as empty)
            "awaiting_human_input": False,
            "human_feedback_requested": False,
            "feedback_context": "",
            "clarification_needed": [],
            "confidence_scores": {},
            "validation_errors": []
        }

        # Run the graph
        result = await graph.graph.ainvoke(initial_state)

        # Update stored conversation state
        updated_conv_state = ConversationState(**result["conversation_state"])
        conversation_states[session_id] = updated_conv_state

        logger.info(f"Successfully processed message. Intent: {result['current_intent']}, Confidence: {result['confidence']}")

        return {
            "response": result["response"],
            "intent": result["current_intent"],
            "confidence": result["confidence"],
            "session_id": session_id,
            "completed": result["completed"],
            "metadata": {
                "flow": updated_conv_state.current_flow,
                "collected_data": updated_conv_state.collected_data
            }
        }

    except Exception as e:
        logger.error(f"Error processing message: {str(e)}", exc_info=True)

        # Return a fallback response
        return {
            "response": "I apologize, but I encountered an error processing your request. Please try again or contact support if the issue persists.",
            "intent": "error",
            "confidence": 0.0,
            "session_id": session_id or str(uuid.uuid4()),
            "completed": True,
            "metadata": {
                "error": str(e),
                "flow": "error_handling"
            }
        }

def create_conversation_graph():
    """Factory function for LangGraph Studio"""
    # Create and return the compiled graph for Studio
    conversation_graph = ConversationGraph()
    return conversation_graph.graph

# For direct Studio compatibility, also export the graph
graph = create_conversation_graph()