import re
from typing import <PERSON><PERSON>
from openai import Async<PERSON>penA<PERSON>
from app.config import settings
from app.utils.logging_config import get_logger

class IntentClassifier:
    def __init__(self):
        # Initialize Async OpenAI client to avoid blocking calls
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        # Configure logger with Bangkok timezone
        self.logger = get_logger(__name__)

        # Rule-based patterns for fallback
        self.intent_patterns = {
            "greeting": [
                r"\b(hello|hi|hey|good morning|good afternoon|good evening)\b",
                r"\b(how are you|what's up)\b"
            ],
            "booking": [
                r"\b(book|appointment|schedule|reserve|reservation)\b",
                r"\b(available|availability|free time|slot)\b",
                r"\b(doctor|consultation|checkup|visit)\b"
            ],
            "info": [
                r"\b(information|info|tell me|what is|how to|help)\b",
                r"\b(hours|location|address|phone|contact)\b",
                r"\b(services|treatment|procedure)\b"
            ],
            "package": [
                r"\b(package|medical package|health package|wellness package)\b",
                r"\b(vaccine|vaccination|botox|aesthetic|surgery)\b",
                r"\b(checkup|health check|screening|examination)\b",
                r"\b(treatment|procedure|therapy|consultation)\b"
            ]
        }

    async def classify_intent(self, message: str) -> Tuple[str, float]:
        """
        Classify user intent using OpenAI or fallback to rule-based classification
        """
        if self.client and settings.OPENAI_API_KEY:
            return await self._classify_with_openai(message)
        else:
            return self._classify_with_rules(message)

    async def _classify_with_openai(self, message: str) -> Tuple[str, float]:
        """Use Async OpenAI for intent classification (non-blocking)"""
        try:
            response = await self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an intent classifier for a medical receptionist chatbot.
                        Classify the user message into one of these intents:
                        - booking: User wants to book an appointment or medical service
                        - greeting: User is greeting or starting conversation
                        - info: User wants information about services, hours, location, etc.
                        - package: User wants information about medical packages (vaccines, health checkups, aesthetic treatments, wellness packages)
                        - unknown: Message doesn't fit any category

                        Respond with only the intent name and confidence (0-1) in format: intent,confidence"""
                    },
                    {"role": "user", "content": message}
                ],
                temperature=0.1,
                max_tokens=20
            )

            result = response.choices[0].message.content.strip()
            parts = result.split(',')

            if len(parts) == 2:
                intent = parts[0].strip().lower()
                confidence = float(parts[1].strip())

                if intent in settings.SUPPORTED_INTENTS:
                    return intent, confidence

        except Exception as e:
            self.logger.error(f"OpenAI classification error: {e}")

        # Fallback to rule-based
        return self._classify_with_rules(message)

    def _classify_with_rules(self, message: str) -> Tuple[str, float]:
        """Rule-based intent classification as fallback"""
        message_lower = message.lower()

        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, message_lower):
                    return intent, 0.8

        return "unknown", 0.5
