"""
Logging configuration with UTC+7 (Bangkok) timezone
"""

import logging
import os
from datetime import datetime, timezone, timedelta
from pathlib import Path
import sys

class BangkokFormatter(logging.Formatter):
    """Custom formatter that uses UTC+7 (Bangkok) timezone"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # UTC+7 timezone (Bangkok)
        self.bangkok_tz = timezone(timedelta(hours=7))
    
    def formatTime(self, record, datefmt=None):
        """Override formatTime to use Bangkok timezone"""
        dt = datetime.fromtimestamp(record.created, tz=self.bangkok_tz)
        if datefmt:
            return dt.strftime(datefmt)
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S %Z')

def setup_logging(log_level: str = "INFO", log_dir: str = "log"):
    """
    Setup logging configuration with UTC+7 timezone and file output
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to save log files
    """
    
    # Create log directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Generate log filename with Bangkok timestamp
    bangkok_tz = timezone(timedelta(hours=7))
    now = datetime.now(bangkok_tz)
    log_filename = f"receptionist_chatbot_{now.strftime('%Y%m%d_%H%M%S')}.log"
    log_file_path = log_path / log_filename
    
    # Create formatter with Bangkok timezone
    formatter = BangkokFormatter(
        fmt='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S %Z'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # File handler
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setLevel(getattr(logging, log_level.upper()))
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Log the setup
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured - Level: {log_level}, File: {log_file_path}")
    logger.info(f"Bangkok time: {now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    return log_file_path

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name"""
    return logging.getLogger(name)
