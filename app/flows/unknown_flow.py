from typing import Dict, Any
from app.models.schemas import ConversationState

async def flow_unknown_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Handle unknown intent - fallback handler for out-of-scope input
    """

    # Provide helpful fallback response
    response = """I'm not sure I understand what you're looking for. I can help you with:

🗓️ **Booking appointments** - Schedule consultations, checkups, or treatments
👋 **General information** - Hours, location, services, contact details
📦 **Medical packages** - Vaccines, health checkups, aesthetic treatments, wellness packages
💬 **General questions** - Ask me anything about our services

Could you please rephrase your request or let me know which of these areas I can help you with?"""

    # Update conversation state
    state.current_flow = "unknown"
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "unknown"
    })

    return {
        "response": response,
        "next_action": "await_input",
        "state": state,
        "completed": True
    }
