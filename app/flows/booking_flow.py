from typing import Dict, Any
import re
from app.models.schemas import ConversationState, BookingData

async def flow_booking_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Handle booking intent - gathers service, date, time, and confirms booking
    """
    # Initialize booking data if not exists
    if "booking_data" not in state.collected_data:
        state.collected_data["booking_data"] = BookingData().dict()
    
    booking_data = BookingData(**state.collected_data["booking_data"])
    
    # Extract information from message
    extracted_info = _extract_booking_info(message)
    
    # Update booking data with extracted info
    if extracted_info.get("service"):
        booking_data.service = extracted_info["service"]
    if extracted_info.get("date"):
        booking_data.date = extracted_info["date"]
    if extracted_info.get("time"):
        booking_data.time = extracted_info["time"]
    
    # Determine next step based on what's missing
    response, next_action = _determine_booking_response(booking_data)
    
    # Update state
    state.current_flow = "booking"
    state.collected_data["booking_data"] = booking_data.dict()
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "booking"
    })
    
    return {
        "response": response,
        "next_action": next_action,
        "state": state,
        "completed": booking_data.confirmed
    }

def _extract_booking_info(message: str) -> Dict[str, str]:
    """Extract booking information from user message"""
    info = {}
    message_lower = message.lower()
    
    # Extract service type
    services = ["consultation", "checkup", "appointment", "doctor", "dentist", "therapy"]
    for service in services:
        if service in message_lower:
            info["service"] = service
            break
    
    # Extract date patterns
    date_patterns = [
        r"\b(tomorrow|today)\b",
        r"\b(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b",
        r"\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b",
        r"\b(next week|this week)\b"
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, message_lower)
        if match:
            info["date"] = match.group(1)
            break
    
    # Extract time patterns
    time_patterns = [
        r"\b(\d{1,2}:\d{2}\s*(?:am|pm)?)\b",
        r"\b(\d{1,2}\s*(?:am|pm))\b",
        r"\b(morning|afternoon|evening)\b"
    ]
    
    for pattern in time_patterns:
        match = re.search(pattern, message_lower)
        if match:
            info["time"] = match.group(1)
            break
    
    return info

def _determine_booking_response(booking_data: BookingData) -> tuple[str, str]:
    """Determine appropriate response based on collected booking data"""
    
    if not booking_data.service:
        return "What type of service would you like to book? (consultation, checkup, etc.)", "await_service"
    
    if not booking_data.date:
        return f"Great! You'd like to book a {booking_data.service}. What date would you prefer?", "await_date"
    
    if not booking_data.time:
        return f"Perfect! {booking_data.service} on {booking_data.date}. What time works best for you?", "await_time"
    
    if not booking_data.confirmed:
        confirmation_msg = f"Let me confirm your booking:\n"
        confirmation_msg += f"Service: {booking_data.service}\n"
        confirmation_msg += f"Date: {booking_data.date}\n"
        confirmation_msg += f"Time: {booking_data.time}\n"
        confirmation_msg += "Would you like to confirm this booking? (yes/no)"
        return confirmation_msg, "await_confirmation"
    
    return "Your booking has been confirmed! You'll receive a confirmation email shortly.", "completed"
