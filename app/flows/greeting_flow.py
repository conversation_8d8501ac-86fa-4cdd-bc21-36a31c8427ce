from typing import Dict, Any
from app.models.schemas import ConversationState

async def flow_greeting_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Handle greeting intent - returns a standard greeting response
    """
    greetings = [
        "Hello! Welcome to our receptionist service. How can I help you today?",
        "Hi there! I'm here to assist you. What can I do for you?",
        "Good day! How may I assist you today?",
        "Hello! I'm your virtual receptionist. How can I help?"
    ]
    
    # Simple greeting selection based on time or random
    import random
    greeting = random.choice(greetings)
    
    # Update conversation state
    state.current_flow = "greeting"
    state.conversation_history.append({
        "user": message,
        "bot": greeting,
        "intent": "greeting"
    })
    
    return {
        "response": greeting,
        "next_action": "await_input",
        "state": state,
        "completed": True
    }
