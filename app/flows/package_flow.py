from typing import Dict, Any, TypedDict, Annotated, List, Optional
import re
import sqlite3
import os
import json
import logging
import asyncio
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from app.models.schemas import ConversationState
from app.config import settings
from openai import AsyncOpenAI

# Configure logger with Bangkok timezone
from app.utils.logging_config import get_logger
logger = get_logger(__name__)

# Initialize Async OpenAI client to avoid blocking calls
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None

# Define the subgraph state with HIL support
class MedicalPackageSubgraphState(TypedDict):
    """State for the medical package subgraph with 4 required fields and HIL support"""
    messages: Annotated[List[BaseMessage], add_messages]
    user_input: str
    extracted_slots: Dict[str, Any]  # Will contain: category, goal_tags, description_short, target_group
    missing_fields: List[str]  # Track which fields are still missing
    query_results: List[Dict[str, Any]]
    generated_sql: str  # Store the LLM-generated SQL query
    response: str
    completed: bool
    iteration_count: int  # Track how many times we've looped

    # Human-in-the-loop specific fields
    awaiting_human_input: bool  # Flag to indicate if waiting for human input
    human_feedback_requested: bool  # Flag to indicate if human feedback was requested
    feedback_context: str  # Context for what kind of feedback is needed
    clarification_needed: List[str]  # Specific clarifications needed from human
    confidence_scores: Dict[str, float]  # Confidence scores for extracted fields
    validation_errors: List[str]  # Any validation errors that need human attention

# Database path from configuration
DB_PATH = settings.DATABASE_PATH

async def flow_package_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Main medical package flow entry point - creates and runs the subgraph
    """
    # Create the package subgraph
    subgraph = create_package_subgraph()

    # Initialize subgraph state with 4 required fields
    required_fields = ["category", "goal_tags", "description_short", "target_group"]
    initial_state: MedicalPackageSubgraphState = {
        "messages": [HumanMessage(content=message)],
        "user_input": message,
        "extracted_slots": {},
        "missing_fields": required_fields.copy(),
        "query_results": [],
        "generated_sql": "",
        "response": "",
        "completed": False,
        "iteration_count": 0
    }

    # Run the subgraph
    result = await subgraph.ainvoke(initial_state)

    # Update conversation state
    state.current_flow = "package"
    state.conversation_history.append({
        "user": message,
        "bot": result["response"],
        "intent": "package",
        "subgraph": "medical_package_search"
    })

    return {
        "response": result["response"],
        "next_action": "completed" if result["completed"] else "continue",
        "state": state,
        "completed": result["completed"]
    }

def create_package_subgraph() -> StateGraph:
    """Create the medical package search subgraph with HIL support and dynamic looping"""

    # Define the subgraph with interrupt support
    workflow = StateGraph(MedicalPackageSubgraphState)

    # Add nodes including HIL nodes (6 nodes total after merging)
    workflow.add_node("extract_slots", extract_slots_node)  # Input processing + extraction
    workflow.add_node("validate_and_check_completion", validate_and_check_completion_node)  # Merged validation + completion
    workflow.add_node("request_clarification", request_clarification_node)  # HIL clarification node
    workflow.add_node("human_feedback", human_feedback_node)  # HIL feedback collection node
    workflow.add_node("build_query", build_query_node)
    workflow.add_node("validate_query", validate_query_node)
    workflow.add_node("final_answer", final_answer_node)

    # Set entry point (now directly to extract_slots)
    workflow.set_entry_point("extract_slots")

    # Add edges with HIL conditional routing (updated for merged node)
    workflow.add_edge("extract_slots", "validate_and_check_completion")

    # Conditional edge: merged validation and completion check
    workflow.add_conditional_edges(
        "validate_and_check_completion",
        should_request_human_feedback_or_continue,
        {
            "request_feedback": "request_clarification",
            "continue": "extract_slots",
            "build_query": "build_query"
        }
    )

    # HIL feedback flow
    workflow.add_edge("request_clarification", "human_feedback")
    workflow.add_edge("human_feedback", "extract_slots")  # Loop back to re-extract with new info

    workflow.add_edge("build_query", "validate_query")
    workflow.add_conditional_edges(
        "validate_query",
        should_retry_query,
        {
            "retry": "build_query",
            "proceed": "final_answer"
        }
    )
    workflow.add_edge("final_answer", END)

    # Compile with interrupt support for HIL
    return workflow.compile(interrupt_before=["human_feedback"])

# Subgraph Node Functions

async def extract_slots_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    Combined Node: Process input and extract the 4 required fields with confidence scoring
    Merged functionality from await_input_node and original extract_slots_node
    """

    # === INPUT PROCESSING (from await_input_node) ===

    # Initialize HIL fields if not present
    if "awaiting_human_input" not in state:
        state["awaiting_human_input"] = False
    if "human_feedback_requested" not in state:
        state["human_feedback_requested"] = False
    if "feedback_context" not in state:
        state["feedback_context"] = ""
    if "clarification_needed" not in state:
        state["clarification_needed"] = []
    if "confidence_scores" not in state:
        state["confidence_scores"] = {}
    if "validation_errors" not in state:
        state["validation_errors"] = []

    # Get the latest user input
    if state["messages"]:
        latest_message = state["messages"][-1]
        if hasattr(latest_message, 'content'):
            state["user_input"] = latest_message.content

    # Increment iteration count
    state["iteration_count"] = state.get("iteration_count", 0) + 1

    # Generate context-aware response based on missing fields and iteration
    missing_fields = state.get("missing_fields", [])
    extracted_slots = state.get("extracted_slots", {})

    if state["iteration_count"] == 1:
        # First interaction - general welcome
        response = "I'll help you find the perfect medical package. Please tell me about what you're looking for."
    else:
        # Subsequent interactions - ask for missing fields
        if missing_fields:
            field_prompts = {
                "category": "What type of medical service are you interested in? (e.g., aesthetic treatments, vaccines, health checkups)",
                "goal_tags": "What are your main goals or concerns? (e.g., beauty, health screening, prevention)",
                "description_short": "Can you briefly describe what specific treatment or service you need?",
                "target_group": "Who is this for? (e.g., adults, children, seniors, specific age group)"
            }

            # Create a tailored prompt for missing fields
            response = "I still need some information to find the best packages for you:\n\n"
            for field in missing_fields:
                if field in field_prompts:
                    response += f"• {field_prompts[field]}\n"

            # Show what we already have
            if extracted_slots:
                response += f"\nSo far I have: {', '.join(f'{k}: {v}' for k, v in extracted_slots.items())}"
        else:
            response = "Great! I have all the information I need. Let me search for packages that match your requirements."

    # Add AI message to conversation
    state["messages"].append(AIMessage(content=response))
    state["response"] = response

    # === FIELD EXTRACTION (original extract_slots_node logic) ===

    user_input = state["user_input"]
    extracted_slots = state["extracted_slots"].copy()
    confidence_scores = state.get("confidence_scores", {})
    user_lower = user_input.lower()

    # Extract category - map user input to database categories
    category_mapping = {
        "aesthetic": ["botox", "filler", "surgery", "aesthetic", "beauty", "cosmetic", "face lift", "breast", "ศัลยกรรมตกแต่ง"],
        "vaccine": ["vaccine", "vaccination", "immunization", "shot", "hpv", "flu", "hepatitis", "วัคซีน"],
        "checkup": ["checkup", "health check", "screening", "examination", "test", "blood test", "ตรวจสุขภาพ"],
        "wellness": ["wellness", "spa", "massage", "therapy", "relaxation", "สุขภาพ"]
    }

    if "category" not in extracted_slots:
        best_match = None
        best_confidence = 0.0
        for category, keywords in category_mapping.items():
            matches = [keyword for keyword in keywords if keyword in user_lower]
            if matches:
                # Calculate confidence based on number and specificity of matches
                confidence = min(0.9, len(matches) * 0.3 + 0.4)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = category

        if best_match:
            extracted_slots["category"] = best_match
            confidence_scores["category"] = best_confidence

    # Extract goal_tags - identify user goals and concerns (be very specific)
    goal_keywords = {
        "beauty": ["beauty", "beautiful", "pretty", "attractive", "ความงาม"],
        "health": ["health", "healthy", "wellness", "สุขภาพ"],
        "prevention": ["prevent", "prevention", "protection", "avoid", "ป้องกัน"],
        "screening": ["screening", "screen", "detect", "early detection", "ตรวจ"],
        "confidence": ["confidence", "self-esteem", "boost", "มั่นใจ"],
        "anti-aging": ["anti-aging", "aging", "young", "youthful", "wrinkle", "ต้านวัย"]
    }

    if "goal_tags" not in extracted_slots:
        found_goals = []
        goal_confidence = 0.0
        for goal, keywords in goal_keywords.items():
            # Use exact word matching to avoid false positives
            if any(f" {keyword} " in f" {user_lower} " or user_lower.startswith(keyword) or user_lower.endswith(keyword) for keyword in keywords):
                found_goals.append(goal)
                goal_confidence += 0.2  # Increase confidence for each goal found
        # Only add if we found specific goal keywords
        if found_goals:
            extracted_slots["goal_tags"] = ", ".join(found_goals)
            confidence_scores["goal_tags"] = min(0.9, goal_confidence + 0.3)

    # Extract description_short - capture specific treatment mentions (be more specific)
    treatment_keywords = [
        "botox", "filler", "surgery", "vaccine", "vaccination", "checkup", "screening",
        "examination", "therapy", "procedure", "consultation", "injection", "treatment"
    ]

    if "description_short" not in extracted_slots:
        found_treatments = []
        for keyword in treatment_keywords:
            if keyword in user_lower:
                found_treatments.append(keyword)
        # Only add if we found specific treatment keywords
        if found_treatments:
            extracted_slots["description_short"] = " ".join(found_treatments)
            # Higher confidence for specific treatment mentions
            confidence_scores["description_short"] = min(0.9, len(found_treatments) * 0.4 + 0.3)

    # Extract target_group - identify who the service is for (be more specific)
    target_keywords = {
        "adults": ["adult", "adults", "grown-up", "ผู้ใหญ่"],
        "children": ["child", "children", "kid", "kids", "baby", "infant", "เด็ก"],
        "seniors": ["senior", "seniors", "elderly", "old people", "ผู้สูงอายุ"],
        "women": ["woman", "women", "female", "lady", "ladies", "ผู้หญิง"],
        "men": ["man", "men", "male", "gentleman", "ผู้ชาย"],
        "teenagers": ["teen", "teenager", "teenagers", "adolescent", "วัยรุ่น"]
    }

    if "target_group" not in extracted_slots:
        best_target_confidence = 0.0
        best_target = None
        for target, keywords in target_keywords.items():
            matches = [keyword for keyword in keywords if f" {keyword} " in f" {user_lower} " or user_lower.startswith(keyword) or user_lower.endswith(keyword)]
            if matches:
                confidence = min(0.9, len(matches) * 0.4 + 0.4)
                if confidence > best_target_confidence:
                    best_target_confidence = confidence
                    best_target = target

        if best_target:
            extracted_slots["target_group"] = best_target
            confidence_scores["target_group"] = best_target_confidence

    # Update state and missing fields
    state["extracted_slots"] = extracted_slots
    state["confidence_scores"] = confidence_scores

    # Update missing fields list
    required_fields = ["category", "goal_tags", "description_short", "target_group"]
    missing_fields = [field for field in required_fields if field not in extracted_slots]
    state["missing_fields"] = missing_fields

    return state



# Conditional routing functions
def should_continue_extraction(state: MedicalPackageSubgraphState) -> str:
    """Determine if we should continue extracting or proceed to build query"""
    missing_fields = state.get("missing_fields", [])
    iteration_count = state.get("iteration_count", 0)

    # Prevent infinite loops - after 5 iterations, proceed anyway
    if iteration_count > 5:
        logger.warning("Maximum iterations reached, proceeding to build query")
        return "build_query"

    # Only proceed to build query if ALL 4 fields are captured
    if not missing_fields:
        logger.info("All 4 required fields captured, proceeding to build query")
        return "build_query"
    else:
        logger.info(f"Missing fields: {missing_fields}, continuing extraction")
        return "continue"

def should_retry_query(state: MedicalPackageSubgraphState) -> str:
    """Determine if we should retry query generation or proceed"""
    generated_sql = state.get("generated_sql", "")

    # Simple validation - check if SQL was generated
    if not generated_sql or "SELECT" not in generated_sql.upper():
        return "retry"
    else:
        return "proceed"

async def build_query_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    Function Node: Use LLM to generate SQL query based on extracted fields
    """
    extracted_slots = state["extracted_slots"]

    # Create system prompt for LLM SQL generation
    system_message = '''
You are a helpful database assistant.
Use the following database schema when creating your answers:

- package_data (package_id, name_th, category, goal_tags, description_short, description_full, target_group, duration, price_option, price_min, price_max, currency, note, available_for, payment_link, image_url, name_en, examination_list)

Include column name headers in the query results.

Always provide your answer in the JSON format below:

{ "query": "your-query" }

Output ONLY JSON.
In the preceding JSON response, substitute "your-query" with SQLite to retrieve the requested data.
Do not use MySQL syntax.
'''

    # Create user prompt based on extracted fields
    user_prompt = f"""
Generate a SQLite query to find medical packages based on these criteria:
- Category: {extracted_slots.get('category', 'any')}
- Goal Tags: {extracted_slots.get('goal_tags', 'any')}
- Description: {extracted_slots.get('description_short', 'any')}
- Target Group: {extracted_slots.get('target_group', 'any')}

The query should:
1. Select package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max
2. Use LIKE operators for text matching
3. Limit results to 10
4. Handle cases where criteria might be 'any' (meaning no filter for that field)
"""

    try:
        if openai_client:
            # Use Async OpenAI to generate SQL (non-blocking)
            response = await openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            # Parse the JSON response
            response_content = response.choices[0].message.content.strip()
            logger.info(f"LLM Response: {response_content}")

            # Extract JSON from response
            try:
                json_response = json.loads(response_content)
                generated_sql = json_response.get("query", "")
                state["generated_sql"] = generated_sql
                logger.info(f"Generated SQL: {generated_sql}")
            except json.JSONDecodeError:
                logger.error("Failed to parse JSON from LLM response")
                state["generated_sql"] = ""

        else:
            # Fallback to rule-based query generation
            logger.warning("OpenAI client not available, using fallback query generation")
            state["generated_sql"] = generate_fallback_query(extracted_slots)

    except Exception as e:
        logger.error(f"Error generating SQL with LLM: {e}")
        state["generated_sql"] = generate_fallback_query(extracted_slots)

    return state

def generate_fallback_query(extracted_slots: Dict[str, Any]) -> str:
    """Generate a fallback SQL query when LLM is not available"""
    conditions = []

    if extracted_slots.get('category') and extracted_slots['category'] != 'any':
        category_mapping = {
            "aesthetic": "ศัลยกรรมตกแต่ง",
            "vaccine": "วัคซีน",
            "checkup": "ตรวจสุขภาพ",
            "wellness": "สุขภาพ"
        }
        db_category = category_mapping.get(extracted_slots['category'])
        if db_category:
            conditions.append(f"category LIKE '%{db_category}%'")

    if extracted_slots.get('goal_tags') and extracted_slots['goal_tags'] != 'any':
        conditions.append(f"goal_tags LIKE '%{extracted_slots['goal_tags']}%'")

    if extracted_slots.get('description_short') and extracted_slots['description_short'] != 'any':
        conditions.append(f"description_short LIKE '%{extracted_slots['description_short']}%'")

    if extracted_slots.get('target_group') and extracted_slots['target_group'] != 'any':
        conditions.append(f"target_group LIKE '%{extracted_slots['target_group']}%'")

    where_clause = " AND ".join(conditions) if conditions else "1=1"

    return f"""
    SELECT package_id, name_en, category, goal_tags, description_short, target_group, price_min, price_max
    FROM package_data
    WHERE {where_clause}
    LIMIT 10
    """

async def validate_query_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    Function Node: Validate and execute the generated SQL query (async)
    """
    generated_sql = state.get("generated_sql", "")

    if not generated_sql:
        logger.error("No SQL query generated")
        state["query_results"] = []
        return state

    try:
        # Execute the generated SQL query using asyncio.to_thread for non-blocking operation
        logger.info(f"Executing LLM-generated query: {generated_sql}")

        results = await asyncio.to_thread(_execute_sql_query, generated_sql)

        logger.info(f"Query returned {len(results)} results")
        state["query_results"] = results

    except sqlite3.Error as e:
        logger.error(f"Database error executing LLM query: {e}")
        # Try fallback query
        fallback_sql = generate_fallback_query(state["extracted_slots"])
        try:
            results = await asyncio.to_thread(_execute_sql_query, fallback_sql)
            state["query_results"] = results
            logger.info(f"Fallback query returned {len(results)} results")
        except Exception as fallback_error:
            logger.error(f"Fallback query also failed: {fallback_error}")
            state["query_results"] = []

    except Exception as e:
        logger.error(f"Unexpected error executing query: {e}")
        state["query_results"] = []

    return state

def _execute_sql_query(sql_query: str) -> List[Dict[str, Any]]:
    """
    Helper function to execute SQL query synchronously (to be called with asyncio.to_thread)
    """
    import sqlite3

    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Enable column access by name
    cursor = conn.cursor()
    cursor.execute(sql_query)
    results = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return results

async def final_answer_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    Function Node: Generate natural language response from query results
    """
    query_results = state["query_results"]
    extracted_slots = state["extracted_slots"]
    missing_fields = state.get("missing_fields", [])

    # Check if we have enough information
    if missing_fields:
        response = f"I still need more information: {', '.join(missing_fields)}. Please provide these details to find the best packages for you."
        state["completed"] = False  # Need more information
    elif not query_results:
        # No results found
        response = f"""Sorry, I couldn't find any medical packages matching your criteria:

🔍 **Search Criteria:**
• Category: {extracted_slots.get('category', 'Not specified')}
• Goals: {extracted_slots.get('goal_tags', 'Not specified')}
• Description: {extracted_slots.get('description_short', 'Not specified')}
• Target Group: {extracted_slots.get('target_group', 'Not specified')}

Could you try different search criteria or be more specific about what you're looking for?"""
        state["completed"] = True
    elif len(query_results) == 1:
        # Single result - provide detailed information
        package = query_results[0]
        response = f"""📦 **{package['name_en']}**

🆔 Package ID: {package['package_id']}
🏷️ Category: {package['category']}
🎯 Goals: {package.get('goal_tags', 'N/A')}
👥 Target Group: {package.get('target_group', 'N/A')}
💰 Price: {package['price_min']:,.0f}"""

        if package['price_max'] != package['price_min']:
            response += f" - {package['price_max']:,.0f}"
        response += " THB"

        if package.get('description_short'):
            response += f"\n📝 Description: {package['description_short']}"

        response += "\n\nWould you like more information about this package?"
        state["completed"] = True

    else:
        # Multiple results - provide summary list
        response = f"""🎉 I found {len(query_results)} medical packages matching your criteria:

🔍 **Your Search:**
• Category: {extracted_slots.get('category', 'Any')}
• Goals: {extracted_slots.get('goal_tags', 'Any')}
• Target Group: {extracted_slots.get('target_group', 'Any')}

📋 **Results:**

"""

        for i, package in enumerate(query_results[:5], 1):  # Show max 5 results
            price_str = f"{package['price_min']:,.0f}"
            if package['price_max'] != package['price_min']:
                price_str += f" - {package['price_max']:,.0f}"
            price_str += " THB"

            response += f"{i}. **{package['name_en']}** ({package['package_id']})\n"
            response += f"   💰 {price_str}\n"
            response += f"   🎯 {package.get('goal_tags', 'N/A')}\n\n"

        if len(query_results) > 5:
            response += f"... and {len(query_results) - 5} more medical packages.\n\n"

        response += "Please provide a specific package ID for detailed information."
        state["completed"] = True

    # Add AI message and mark as completed
    state["messages"].append(AIMessage(content=response))
    state["response"] = response

    return state

# Human-in-the-Loop (HIL) Node Functions

async def validate_and_check_completion_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    Merged HIL Node: Validate extracted information AND check completion in a single operation
    Combines functionality from validate_extraction_node and check_completion_node
    """
    extracted_slots = state.get("extracted_slots", {})
    confidence_scores = state.get("confidence_scores", {})
    missing_fields = state.get("missing_fields", [])

    # Initialize validation fields
    state["validation_errors"] = []
    state["clarification_needed"] = []

    # === VALIDATION LOGIC (from validate_extraction_node) ===

    # Check confidence scores for extracted fields
    low_confidence_threshold = 0.6
    for field in extracted_slots.keys():
        confidence = confidence_scores.get(field, 0.0)
        if confidence < low_confidence_threshold:
            state["clarification_needed"].append(f"{field} (confidence: {confidence:.2f})")

    # Check for ambiguous or unclear extractions
    if "goal_tags" in extracted_slots:
        goals = extracted_slots["goal_tags"].split(", ")
        if len(goals) > 3:  # Too many goals might indicate confusion
            state["clarification_needed"].append("goal_tags (too many goals specified)")

    # Check for contradictory information
    if "category" in extracted_slots and "description_short" in extracted_slots:
        category = extracted_slots["category"]
        description = extracted_slots["description_short"]

        # Simple contradiction check
        if category == "vaccine" and "surgery" in description:
            state["validation_errors"].append("Contradiction: vaccine category with surgery description")
        elif category == "aesthetic" and "vaccine" in description:
            state["validation_errors"].append("Contradiction: aesthetic category with vaccine description")

    # === COMPLETION LOGIC (from check_completion_node) ===

    # Check if validation issues require human feedback
    has_validation_issues = bool(state["clarification_needed"] or state["validation_errors"])

    if has_validation_issues:
        # Validation issues found - request human feedback
        state["feedback_context"] = "extraction_validation"
        state["human_feedback_requested"] = True
        state["response"] = "I need clarification to ensure accuracy before proceeding."
    elif missing_fields:
        # No validation issues but missing fields - continue extraction
        state["human_feedback_requested"] = False
        state["response"] = f"I still need: {', '.join(missing_fields)}"
    else:
        # No validation issues and all fields complete - proceed to query
        state["human_feedback_requested"] = False
        state["response"] = "Perfect! I have all the information needed. Let me search for packages that match your requirements."

    return state

async def request_clarification_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    HIL Node: Request specific clarification from human before proceeding
    """
    clarification_needed = state.get("clarification_needed", [])
    validation_errors = state.get("validation_errors", [])
    extracted_slots = state.get("extracted_slots", {})

    # Build clarification request
    response_parts = ["I need some clarification to ensure I find the right medical packages for you:\n"]

    if validation_errors:
        response_parts.append("⚠️ **Potential Issues:**")
        for error in validation_errors:
            response_parts.append(f"• {error}")
        response_parts.append("")

    if clarification_needed:
        response_parts.append("🤔 **Please clarify:**")
        for clarification in clarification_needed:
            if "category" in clarification:
                response_parts.append("• What type of medical service are you looking for? (aesthetic, vaccine, checkup, or wellness)")
            elif "goal_tags" in clarification:
                response_parts.append("• What is your main goal? (beauty, health, prevention, screening, confidence, or anti-aging)")
            elif "description_short" in clarification:
                response_parts.append("• Can you be more specific about the treatment or procedure you want?")
            elif "target_group" in clarification:
                response_parts.append("• Who is this medical package for? (adults, children, seniors, women, men, or teenagers)")
        response_parts.append("")

    # Show current understanding
    if extracted_slots:
        response_parts.append("📋 **My current understanding:**")
        for field, value in extracted_slots.items():
            confidence = state.get("confidence_scores", {}).get(field, 0.0)
            response_parts.append(f"• {field}: {value} (confidence: {confidence:.0%})")
        response_parts.append("")

    response_parts.append("Please provide the clarification so I can help you better! 🙏")

    response = "\n".join(response_parts)

    # Set state for human feedback
    state["response"] = response
    state["awaiting_human_input"] = True
    state["feedback_context"] = "clarification_request"

    # Add AI message
    state["messages"].append(AIMessage(content=response))

    return state

async def human_feedback_node(state: MedicalPackageSubgraphState) -> MedicalPackageSubgraphState:
    """
    HIL Node: Collect and process human feedback using interrupt()
    This node will be interrupted to collect human input
    """
    # This node will be interrupted by the graph execution
    # When resumed, it will have the human's response in the latest message

    if state["messages"]:
        latest_message = state["messages"][-1]
        if hasattr(latest_message, 'content'):
            human_feedback = latest_message.content

            # Process the human feedback
            state["user_input"] = human_feedback
            state["awaiting_human_input"] = False
            state["human_feedback_requested"] = False

            # Clear previous clarification requests
            state["clarification_needed"] = []
            state["validation_errors"] = []

            # Reset confidence scores for re-extraction
            state["confidence_scores"] = {}

            # Add acknowledgment
            ack_response = "Thank you for the clarification! Let me process this information."
            state["response"] = ack_response
            state["messages"].append(AIMessage(content=ack_response))

    return state

# HIL Routing Functions

def should_request_human_feedback_or_continue(state: MedicalPackageSubgraphState) -> str:
    """
    Merged routing function: Determine if human feedback is needed,
    if extraction should continue, or if ready to build query
    Combines logic from should_request_human_feedback and should_continue_extraction
    """
    human_feedback_requested = state.get("human_feedback_requested", False)
    clarification_needed = state.get("clarification_needed", [])
    validation_errors = state.get("validation_errors", [])
    missing_fields = state.get("missing_fields", [])
    iteration_count = state.get("iteration_count", 0)

    # Priority 1: Request feedback if there are validation issues or low confidence
    if human_feedback_requested or clarification_needed or validation_errors:
        logger.info(f"Requesting human feedback: clarifications={clarification_needed}, errors={validation_errors}")
        return "request_feedback"

    # Priority 2: Check completion and iteration limits
    # Prevent infinite loops - after 5 iterations, proceed anyway
    if iteration_count > 5:
        logger.warning("Maximum iterations reached, proceeding to build query")
        return "build_query"

    # Priority 3: Check if all fields are captured
    if not missing_fields:
        logger.info("All 4 required fields captured, proceeding to build query")
        return "build_query"
    else:
        logger.info(f"Missing fields: {missing_fields}, continuing extraction")
        return "continue"

# Legacy routing functions (kept for compatibility)
def should_request_human_feedback(state: MedicalPackageSubgraphState) -> str:
    """Legacy function - use should_request_human_feedback_or_continue instead"""
    return should_request_human_feedback_or_continue(state)