from typing import Dict, Any
import re
from app.models.schemas import ConversationState

# Sample FAQ database
FAQ_DATABASE = {
    "hours": {
        "keywords": ["hours", "open", "close", "time", "schedule"],
        "response": "Our office hours are Monday-Friday 8:00 AM to 6:00 PM, and Saturday 9:00 AM to 2:00 PM. We're closed on Sundays."
    },
    "location": {
        "keywords": ["location", "address", "where", "directions"],
        "response": "We're located at 123 Healthcare Avenue, Medical District. Free parking is available on-site."
    },
    "contact": {
        "keywords": ["phone", "contact", "call", "number", "email"],
        "response": "You can reach us at (************* or email <NAME_EMAIL>. For emergencies, please call 911."
    },
    "services": {
        "keywords": ["services", "treatment", "procedure", "what do you do", "specialties"],
        "response": "We offer general consultations, preventive care, diagnostic services, minor procedures, and specialist referrals. Please ask about specific services for more details."
    },
    "insurance": {
        "keywords": ["insurance", "coverage", "payment", "cost", "price"],
        "response": "We accept most major insurance plans. Please bring your insurance card to verify coverage. We also offer payment plans for uninsured patients."
    },
    "appointment": {
        "keywords": ["appointment", "booking", "schedule", "availability"],
        "response": "To book an appointment, you can call us, use our online portal, or I can help you schedule one right now. What would you prefer?"
    }
}

async def flow_info_start(state: ConversationState, message: str) -> Dict[str, Any]:
    """
    Handle info intent - looks up FAQs and returns answers
    """
    # Find matching FAQ
    response = _find_faq_response(message)
    
    # Update conversation state
    state.current_flow = "info"
    state.conversation_history.append({
        "user": message,
        "bot": response,
        "intent": "info"
    })
    
    return {
        "response": response,
        "next_action": "await_input",
        "state": state,
        "completed": True
    }

def _find_faq_response(message: str) -> str:
    """Find the most relevant FAQ response based on keywords"""
    message_lower = message.lower()
    
    # Score each FAQ category based on keyword matches
    scores = {}
    for category, faq_data in FAQ_DATABASE.items():
        score = 0
        for keyword in faq_data["keywords"]:
            if keyword in message_lower:
                score += 1
        scores[category] = score
    
    # Find the category with the highest score
    if scores and max(scores.values()) > 0:
        best_category = max(scores, key=scores.get)
        return FAQ_DATABASE[best_category]["response"]
    
    # Default response if no specific FAQ matches
    return """I'd be happy to help you with information! Here are some common topics I can assist with:
    
• Office hours and location
• Contact information  
• Services and treatments
• Insurance and payment
• Appointment booking

Please let me know what specific information you need, or feel free to ask me anything else!"""
