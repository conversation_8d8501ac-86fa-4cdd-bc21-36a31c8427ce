# LangGraph State Transition Analysis Report

Generated: 2025-05-30 02:17:55

## Main Conversation Graph

### Nodes:
- **__start__**
- **classify_intent**
- **flow_greeting_start**
- **flow_booking_start**
- **flow_info_start**
- **flow_package_start**
- **flow_unknown_start**
- **__end__**

### Edges:
- __start__ → classify_intent
- classify_intent → flow_booking_start
- classify_intent → flow_greeting_start
- classify_intent → flow_info_start
- classify_intent → flow_package_start
- classify_intent → flow_unknown_start
- flow_booking_start → __end__
- flow_greeting_start → __end__
- flow_info_start → __end__
- flow_package_start → __end__
- flow_unknown_start → __end__

### Graph Properties:
- **Total Nodes**: 8
- **Total Edges**: 11
- **Graph Type**: Main conversation router

## Package Subgraph

### Nodes:
- **__start__**
- **await_input**
- **extract_slots**
- **check_completion**
- **build_query**
- **validate_query**
- **final_answer**
- **__end__**

### Edges:
- __start__ → await_input
- await_input → extract_slots
- build_query → validate_query
- check_completion → await_input
- check_completion → build_query
- extract_slots → check_completion
- validate_query → build_query
- validate_query → final_answer
- final_answer → __end__

### Graph Properties:
- **Total Nodes**: 8
- **Total Edges**: 9
- **Graph Type**: Dynamic looping subgraph

### Conditional Edges:
- Multiple conditional routing edges present

## Graph Characteristics

### Main Graph:
- **Total Nodes**: 8
- **Total Edges**: 11
- **Graph Type**: Main conversation router
- **Parallelism**: Sequential intent-based routing

### Package Subgraph:
- **Total Nodes**: 8
- **Total Edges**: 9
- **Graph Type**: Dynamic looping subgraph
- **Parallelism**: Human-in-the-loop with conditional branching

## Technical Implementation

### Async Operations:
- ✅ OpenAI API calls (AsyncOpenAI)
- ✅ SQLite database operations (asyncio.to_thread)
- ✅ Intent classification
- ✅ LLM-based SQL generation

### State Management:
- **Main State**: GraphState with conversation context
- **Subgraph State**: MedicalPackageSubgraphState with 4 required fields
- **Persistence**: In-memory conversation states (production: database)

### Key Features:
- **Dynamic Looping**: Package subgraph loops until all 4 fields captured
- **LLM Integration**: GPT-4 for SQL generation, GPT-3.5 for intent classification
- **Non-blocking**: All operations use async patterns
- **Error Handling**: Fallback mechanisms for LLM and database failures
- **Logging**: Bangkok timezone (UTC+7) with structured logging

## Files Generated:
- Main conversation graph PNG and Mermaid source
- Package subgraph PNG and Mermaid source
- ASCII representations for both graphs
- This analysis report

---
*Generated by LangGraph Native Visualization Script*
