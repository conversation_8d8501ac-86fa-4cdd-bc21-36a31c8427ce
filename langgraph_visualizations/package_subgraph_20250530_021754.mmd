---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	await_input(await_input)
	extract_slots(extract_slots)
	check_completion(check_completion)
	build_query(build_query)
	validate_query(validate_query)
	final_answer(final_answer)
	__end__([<p>__end__</p>]):::last
	__start__ --> await_input;
	await_input --> extract_slots;
	build_query --> validate_query;
	check_completion -. &nbsp;continue&nbsp; .-> await_input;
	check_completion -.-> build_query;
	extract_slots --> check_completion;
	validate_query -. &nbsp;retry&nbsp; .-> build_query;
	validate_query -. &nbsp;proceed&nbsp; .-> final_answer;
	final_answer --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
