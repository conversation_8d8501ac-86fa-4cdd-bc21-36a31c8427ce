"""
Create a unified conversation graph that includes the package subgraph as an embedded subgraph
This will generate a single comprehensive visualization showing both main flow and subgraph details
"""

from typing import Dict, Any, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from datetime import datetime
from pathlib import Path

# Import the existing components
from app.models.schemas import ConversationState
from app.services.intent_classifier import IntentClassifier
from app.flows.greeting_flow import flow_greeting_start
from app.flows.booking_flow import flow_booking_start
from app.flows.info_flow import flow_info_start
from app.flows.unknown_flow import flow_unknown_start

# Import package flow components
from app.flows.package_flow import (
    MedicalPackageSubgraphState, 
    await_input_node,
    extract_slots_node,
    check_completion_node,
    build_query_node,
    validate_query_node,
    final_answer_node,
    should_continue_extraction,
    should_retry_query
)

class UnifiedGraphState(TypedDict):
    """Unified state that can handle both main flow and package subgraph"""
    messages: Annotated[list[BaseMessage], add_messages]
    session_id: str
    current_intent: str
    confidence: float
    conversation_state: Dict[str, Any]
    response: str
    completed: bool
    
    # Package subgraph specific fields
    user_input: str
    extracted_slots: Dict[str, Any]
    missing_fields: list[str]
    query_results: list[Dict[str, Any]]
    generated_sql: str
    iteration_count: int

def create_unified_conversation_graph():
    """Create a unified graph that includes the package subgraph as embedded nodes"""
    
    # Define the unified graph
    workflow = StateGraph(UnifiedGraphState)
    
    # Add main flow nodes
    workflow.add_node("classify_intent", classify_intent_node)
    workflow.add_node("flow_greeting_start", greeting_flow_node)
    workflow.add_node("flow_booking_start", booking_flow_node)
    workflow.add_node("flow_info_start", info_flow_node)
    workflow.add_node("flow_unknown_start", unknown_flow_node)
    
    # Add package subgraph nodes directly to main graph
    workflow.add_node("package_await_input", package_await_input_wrapper)
    workflow.add_node("package_extract_slots", package_extract_slots_wrapper)
    workflow.add_node("package_check_completion", package_check_completion_wrapper)
    workflow.add_node("package_build_query", package_build_query_wrapper)
    workflow.add_node("package_validate_query", package_validate_query_wrapper)
    workflow.add_node("package_final_answer", package_final_answer_wrapper)
    
    # Set entry point
    workflow.set_entry_point("classify_intent")
    
    # Add conditional edges from intent classification
    workflow.add_conditional_edges(
        "classify_intent",
        route_intent,
        {
            "greeting": "flow_greeting_start",
            "booking": "flow_booking_start", 
            "info": "flow_info_start",
            "package": "package_await_input",  # Route to package subgraph
            "unknown": "flow_unknown_start"
        }
    )
    
    # Add edges for simple flows to END
    workflow.add_edge("flow_greeting_start", END)
    workflow.add_edge("flow_booking_start", END)
    workflow.add_edge("flow_info_start", END)
    workflow.add_edge("flow_unknown_start", END)
    
    # Add package subgraph edges (embedded in main graph)
    workflow.add_edge("package_await_input", "package_extract_slots")
    workflow.add_edge("package_extract_slots", "package_check_completion")
    
    # Conditional edge for completion check
    workflow.add_conditional_edges(
        "package_check_completion",
        package_should_continue_extraction,
        {
            "continue": "package_await_input",  # Loop back for missing fields
            "build_query": "package_build_query"  # All fields captured
        }
    )
    
    workflow.add_edge("package_build_query", "package_validate_query")
    
    # Conditional edge for query validation
    workflow.add_conditional_edges(
        "package_validate_query", 
        package_should_retry_query,
        {
            "retry": "package_build_query",  # Retry SQL generation
            "proceed": "package_final_answer"  # Query successful
        }
    )
    
    workflow.add_edge("package_final_answer", END)
    
    return workflow.compile()

# Node wrapper functions to adapt package subgraph nodes to unified state

async def classify_intent_node(state: UnifiedGraphState) -> UnifiedGraphState:
    """Classify user intent"""
    intent_classifier = IntentClassifier()
    latest_message = state["messages"][-1].content if state["messages"] else ""
    
    intent, confidence = await intent_classifier.classify_intent(latest_message)
    
    state["current_intent"] = intent
    state["confidence"] = confidence
    state["user_input"] = latest_message
    
    return state

def route_intent(state: UnifiedGraphState) -> str:
    """Route to appropriate flow based on classified intent"""
    return state["current_intent"]

async def greeting_flow_node(state: UnifiedGraphState) -> UnifiedGraphState:
    """Handle greeting flow"""
    message = state["messages"][-1].content if state["messages"] else ""
    conv_state = ConversationState(
        session_id=state["session_id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    result = await flow_greeting_start(conv_state, message)
    
    state["messages"].append(AIMessage(content=result["response"]))
    state["response"] = result["response"]
    state["completed"] = result["completed"]
    
    return state

async def booking_flow_node(state: UnifiedGraphState) -> UnifiedGraphState:
    """Handle booking flow"""
    message = state["messages"][-1].content if state["messages"] else ""
    conv_state = ConversationState(
        session_id=state["session_id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    result = await flow_booking_start(conv_state, message)
    
    state["messages"].append(AIMessage(content=result["response"]))
    state["response"] = result["response"]
    state["completed"] = result["completed"]
    
    return state

async def info_flow_node(state: UnifiedGraphState) -> UnifiedGraphState:
    """Handle info flow"""
    message = state["messages"][-1].content if state["messages"] else ""
    conv_state = ConversationState(
        session_id=state["session_id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    result = await flow_info_start(conv_state, message)
    
    state["messages"].append(AIMessage(content=result["response"]))
    state["response"] = result["response"]
    state["completed"] = result["completed"]
    
    return state

async def unknown_flow_node(state: UnifiedGraphState) -> UnifiedGraphState:
    """Handle unknown flow"""
    message = state["messages"][-1].content if state["messages"] else ""
    conv_state = ConversationState(
        session_id=state["session_id"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    result = await flow_unknown_start(conv_state, message)
    
    state["messages"].append(AIMessage(content=result["response"]))
    state["response"] = result["response"]
    state["completed"] = result["completed"]
    
    return state

# Package subgraph wrapper functions

async def package_await_input_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package await_input node"""
    # Initialize package-specific fields if not present
    if "extracted_slots" not in state:
        state["extracted_slots"] = {}
    if "missing_fields" not in state:
        state["missing_fields"] = ["category", "goal_tags", "description_short", "target_group"]
    if "iteration_count" not in state:
        state["iteration_count"] = 0
    if "query_results" not in state:
        state["query_results"] = []
    if "generated_sql" not in state:
        state["generated_sql"] = ""
    
    # Convert to package subgraph state
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state.get("response", ""),
        "completed": state.get("completed", False),
        "iteration_count": state["iteration_count"]
    }
    
    # Call the original node
    result = await await_input_node(package_state)
    
    # Update unified state
    state["response"] = result["response"]
    state["iteration_count"] = result["iteration_count"]
    state["messages"] = result["messages"]
    
    return state

async def package_extract_slots_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package extract_slots node"""
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state["response"],
        "completed": state["completed"],
        "iteration_count": state["iteration_count"]
    }
    
    result = await extract_slots_node(package_state)
    
    state["extracted_slots"] = result["extracted_slots"]
    state["missing_fields"] = result["missing_fields"]
    
    return state

async def package_check_completion_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package check_completion node"""
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state["response"],
        "completed": state["completed"],
        "iteration_count": state["iteration_count"]
    }
    
    result = await check_completion_node(package_state)
    
    state["response"] = result["response"]
    
    return state

def package_should_continue_extraction(state: UnifiedGraphState) -> str:
    """Determine if package extraction should continue"""
    missing_fields = state.get("missing_fields", [])
    iteration_count = state.get("iteration_count", 0)
    
    if iteration_count > 5:
        return "build_query"
    
    if missing_fields:
        return "continue"
    else:
        return "build_query"

async def package_build_query_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package build_query node"""
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state["response"],
        "completed": state["completed"],
        "iteration_count": state["iteration_count"]
    }
    
    result = await build_query_node(package_state)
    
    state["generated_sql"] = result["generated_sql"]
    
    return state

async def package_validate_query_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package validate_query node"""
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state["response"],
        "completed": state["completed"],
        "iteration_count": state["iteration_count"]
    }
    
    result = await validate_query_node(package_state)
    
    state["query_results"] = result["query_results"]
    
    return state

def package_should_retry_query(state: UnifiedGraphState) -> str:
    """Determine if query should be retried"""
    generated_sql = state.get("generated_sql", "")
    
    if not generated_sql or "SELECT" not in generated_sql.upper():
        return "retry"
    else:
        return "proceed"

async def package_final_answer_wrapper(state: UnifiedGraphState) -> UnifiedGraphState:
    """Wrapper for package final_answer node"""
    package_state: MedicalPackageSubgraphState = {
        "messages": state["messages"],
        "user_input": state["user_input"],
        "extracted_slots": state["extracted_slots"],
        "missing_fields": state["missing_fields"],
        "query_results": state["query_results"],
        "generated_sql": state["generated_sql"],
        "response": state["response"],
        "completed": state["completed"],
        "iteration_count": state["iteration_count"]
    }
    
    result = await final_answer_node(package_state)
    
    state["response"] = result["response"]
    state["completed"] = result["completed"]
    state["messages"] = result["messages"]
    
    return state

def create_unified_visualization():
    """Create and save unified graph visualization"""
    
    print("🎨 Creating Unified Graph Visualization...")
    print("=" * 60)
    
    # Create the unified graph
    unified_graph = create_unified_conversation_graph()
    
    # Create output directory
    output_dir = Path("unified_graph_visualization")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        # Generate Mermaid PNG
        mermaid_png = unified_graph.get_graph().draw_mermaid_png()
        png_path = output_dir / f"unified_conversation_graph_{timestamp}.png"
        
        with open(png_path, "wb") as f:
            f.write(mermaid_png)
        
        print(f"✅ Unified graph PNG saved: {png_path}")
        
        # Generate Mermaid source
        mermaid_source = unified_graph.get_graph().draw_mermaid()
        mermaid_path = output_dir / f"unified_conversation_graph_{timestamp}.mmd"
        
        with open(mermaid_path, "w") as f:
            f.write(mermaid_source)
        
        print(f"✅ Unified graph Mermaid source saved: {mermaid_path}")
        
        # Generate ASCII representation
        ascii_repr = unified_graph.get_graph().draw_ascii()
        ascii_path = output_dir / f"unified_conversation_graph_{timestamp}.txt"
        
        with open(ascii_path, "w") as f:
            f.write(ascii_repr)
        
        print(f"✅ Unified graph ASCII saved: {ascii_path}")
        
        # Print graph statistics
        graph_info = unified_graph.get_graph()
        print(f"\n📊 Unified Graph Statistics:")
        print(f"   • Total Nodes: {len(graph_info.nodes)}")
        print(f"   • Total Edges: {len(graph_info.edges)}")
        print(f"   • Main Flow Nodes: 5")
        print(f"   • Package Subgraph Nodes: 6")
        print(f"   • Conditional Edges: 2")
        
        print(f"\n📋 Node List:")
        for node_name in sorted(graph_info.nodes.keys()):
            node_type = "Main Flow" if not node_name.startswith("package_") else "Package Subgraph"
            print(f"   • {node_name} ({node_type})")
        
        return png_path, mermaid_path, ascii_path
        
    except Exception as e:
        print(f"❌ Error creating unified visualization: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    png_file, mermaid_file, ascii_file = create_unified_visualization()
    
    if png_file:
        print(f"\n🎉 Unified Graph Visualization Complete!")
        print(f"📁 Files created:")
        print(f"   • {png_file} (PNG image)")
        print(f"   • {mermaid_file} (Mermaid source)")
        print(f"   • {ascii_file} (ASCII representation)")
        print(f"\n💡 This single diagram shows:")
        print(f"   ✅ Complete main conversation flow")
        print(f"   ✅ Embedded package subgraph with all nodes")
        print(f"   ✅ Dynamic looping behavior")
        print(f"   ✅ LLM integration points")
        print(f"   ✅ Conditional routing logic")
    else:
        print(f"❌ Failed to create unified visualization")
