"""
Test script to verify the LangGraph Studio setup is working correctly
"""

import asyncio
from langchain_core.messages import HumanMessage
from studio_graph import graph

async def test_graph():
    """Test the conversation graph with sample inputs"""
    
    test_cases = [
        {
            "input": "Hello, how are you?",
            "expected_intent": "greeting"
        },
        {
            "input": "I want to book an appointment",
            "expected_intent": "booking"
        },
        {
            "input": "What are your hours?",
            "expected_intent": "info"
        },
        {
            "input": "I need to send a package",
            "expected_intent": "package"
        }
    ]
    
    print("🧪 Testing LangGraph Studio setup...")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['input']}")
        
        # Create initial state
        initial_state = {
            "messages": [HumanMessage(content=test_case["input"])],
            "session_id": f"test_session_{i}",
            "current_intent": "",
            "confidence": 0.0,
            "conversation_state": {
                "session_id": f"test_session_{i}",
                "current_intent": None,
                "current_flow": None,
                "collected_data": {},
                "conversation_history": [],
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            },
            "response": "",
            "completed": False
        }
        
        try:
            # Run the graph
            result = await graph.ainvoke(initial_state)
            
            print(f"   ✅ Intent: {result['current_intent']}")
            print(f"   ✅ Confidence: {result['confidence']:.2f}")
            print(f"   ✅ Response: {result['response'][:100]}...")
            print(f"   ✅ Completed: {result['completed']}")
            
            # Check if intent matches expected
            if result['current_intent'] == test_case['expected_intent']:
                print(f"   🎯 Intent classification: CORRECT")
            else:
                print(f"   ⚠️  Intent classification: Expected {test_case['expected_intent']}, got {result['current_intent']}")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 Graph testing completed!")

if __name__ == "__main__":
    asyncio.run(test_graph())
