---
config:
  flowchart:
    curve: linear
---
graph TD;
	__start__([<p>__start__</p>]):::first
	extract_slots(extract_slots)
	validate_and_check_completion(validate_and_check_completion)
	request_clarification(request_clarification)
	human_feedback(human_feedback<hr/><small><em>__interrupt = before</em></small>)
	build_query(build_query)
	validate_query(validate_query)
	final_answer(final_answer)
	__end__([<p>__end__</p>]):::last
	__start__ --> extract_slots;
	build_query --> validate_query;
	extract_slots --> validate_and_check_completion;
	human_feedback --> extract_slots;
	request_clarification --> human_feedback;
	validate_and_check_completion -.-> build_query;
	validate_and_check_completion -. &nbsp;continue&nbsp; .-> extract_slots;
	validate_and_check_completion -. &nbsp;request_feedback&nbsp; .-> request_clarification;
	validate_query -. &nbsp;retry&nbsp; .-> build_query;
	validate_query -. &nbsp;proceed&nbsp; .-> final_answer;
	final_answer --> __end__;
	classDef default fill:#f2f0ff,line-height:1.2
	classDef first fill-opacity:0
	classDef last fill:#bfb6fc
