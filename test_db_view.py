import sqlite3
import pandas as pd

# Path to the .db file
db_file_path = 'data/package_data.db'

# Connect to the SQLite database
conn = sqlite3.connect(db_file_path)

# Query to get the first 5 rows from the table
query = "SELECT * FROM package_data LIMIT 5"

# Execute the query and load results into a DataFrame
df = pd.read_sql_query(query, conn)

# Close the connection
conn.close()

# Display the DataFrame
print(df)
