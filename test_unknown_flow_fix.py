"""
Test script to verify unknown_flow.py now has medical package context
"""

import asyncio
from app.flows.unknown_flow import flow_unknown_start
from app.models.schemas import ConversationState
from datetime import datetime

async def test_unknown_flow_medical_context():
    """Test that unknown flow mentions medical packages, not delivery"""
    
    print("🧪 Testing Unknown Flow Medical Package Context...")
    print("=" * 60)
    
    # Create conversation state
    state = ConversationState(
        session_id="test_unknown_flow",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    
    # Test unknown flow response
    result = await flow_unknown_start(state, "I don't understand")
    
    response = result["response"]
    print("📝 Unknown Flow Response:")
    print(response)
    print("\n" + "=" * 60)
    
    # Check for medical package context
    medical_keywords = ["medical packages", "vaccines", "health checkups", "aesthetic treatments", "wellness packages"]
    delivery_keywords = ["send packages", "track deliveries", "shipping", "courier", "parcel"]
    
    medical_found = any(keyword in response.lower() for keyword in medical_keywords)
    delivery_found = any(keyword in response.lower() for keyword in delivery_keywords)
    
    print("🔍 Context Analysis:")
    print(f"   ✅ Medical package context found: {medical_found}")
    print(f"   ✅ No delivery context found: {not delivery_found}")
    
    if medical_found and not delivery_found:
        print("\n🎉 SUCCESS: Unknown flow now has medical package context!")
    else:
        print("\n❌ ISSUE: Unknown flow still has delivery context or missing medical context")
    
    return medical_found and not delivery_found

if __name__ == "__main__":
    asyncio.run(test_unknown_flow_medical_context())
