"""
<PERSON><PERSON><PERSON> to visualize state graphs using LangGraph's built-in utilities
Uses graph.get_graph().draw_mermaid_png() and other native methods
"""

import os
from datetime import datetime
from pathlib import Path

def create_langgraph_visualizations():
    """Create visualizations using LangGraph's native utilities"""

    print("🎨 Creating LangGraph Native Visualizations...")
    print("=" * 60)

    # Create output directory
    output_dir = Path("langgraph_visualizations")
    output_dir.mkdir(exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    try:
        # Import the conversation graph
        from app.services.conversation_graph import create_conversation_graph

        print("📊 1. Main Conversation Graph...")

        # Create the main conversation graph
        main_graph = create_conversation_graph()

        # Generate Mermaid PNG
        try:
            mermaid_png = main_graph.get_graph().draw_mermaid_png()
            main_png_path = output_dir / f"main_conversation_graph_{timestamp}.png"

            with open(main_png_path, "wb") as f:
                f.write(mermaid_png)

            print(f"   ✅ Main graph PNG saved: {main_png_path}")

        except Exception as e:
            print(f"   ❌ Error creating main graph PNG: {e}")

        # Generate Mermaid source
        try:
            mermaid_source = main_graph.get_graph().draw_mermaid()
            main_mermaid_path = output_dir / f"main_conversation_graph_{timestamp}.mmd"

            with open(main_mermaid_path, "w") as f:
                f.write(mermaid_source)

            print(f"   ✅ Main graph Mermaid source saved: {main_mermaid_path}")

        except Exception as e:
            print(f"   ❌ Error creating main graph Mermaid: {e}")

        # Try to get ASCII representation
        try:
            ascii_repr = main_graph.get_graph().draw_ascii()
            main_ascii_path = output_dir / f"main_conversation_graph_{timestamp}.txt"

            with open(main_ascii_path, "w") as f:
                f.write(ascii_repr)

            print(f"   ✅ Main graph ASCII saved: {main_ascii_path}")

        except Exception as e:
            print(f"   ❌ Error creating main graph ASCII: {e}")

    except Exception as e:
        print(f"❌ Error with main conversation graph: {e}")

    try:
        # Import and visualize the package subgraph
        from app.flows.package_flow import create_package_subgraph

        print("\n📦 2. Package Subgraph...")

        # Create the package subgraph
        package_subgraph = create_package_subgraph()

        # Generate Mermaid PNG for subgraph
        try:
            subgraph_mermaid_png = package_subgraph.get_graph().draw_mermaid_png()
            subgraph_png_path = output_dir / f"package_subgraph_{timestamp}.png"

            with open(subgraph_png_path, "wb") as f:
                f.write(subgraph_mermaid_png)

            print(f"   ✅ Package subgraph PNG saved: {subgraph_png_path}")

        except Exception as e:
            print(f"   ❌ Error creating package subgraph PNG: {e}")

        # Generate Mermaid source for subgraph
        try:
            subgraph_mermaid_source = package_subgraph.get_graph().draw_mermaid()
            subgraph_mermaid_path = output_dir / f"package_subgraph_{timestamp}.mmd"

            with open(subgraph_mermaid_path, "w") as f:
                f.write(subgraph_mermaid_source)

            print(f"   ✅ Package subgraph Mermaid source saved: {subgraph_mermaid_path}")

        except Exception as e:
            print(f"   ❌ Error creating package subgraph Mermaid: {e}")

        # Try to get ASCII representation for subgraph
        try:
            subgraph_ascii_repr = package_subgraph.get_graph().draw_ascii()
            subgraph_ascii_path = output_dir / f"package_subgraph_{timestamp}.txt"

            with open(subgraph_ascii_path, "w") as f:
                f.write(subgraph_ascii_repr)

            print(f"   ✅ Package subgraph ASCII saved: {subgraph_ascii_path}")

        except Exception as e:
            print(f"   ❌ Error creating package subgraph ASCII: {e}")

    except Exception as e:
        print(f"❌ Error with package subgraph: {e}")

    return output_dir

def create_graph_analysis_report(output_dir):
    """Create a detailed analysis report of the graphs"""

    print("\n📋 3. Creating Graph Analysis Report...")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = output_dir / f"graph_analysis_report_{timestamp}.md"

    try:
        from app.services.conversation_graph import create_conversation_graph
        from app.flows.package_flow import create_package_subgraph

        main_graph = create_conversation_graph()
        package_subgraph = create_package_subgraph()

        # Get graph information
        main_graph_info = main_graph.get_graph()
        package_graph_info = package_subgraph.get_graph()

        report_content = f"""# LangGraph State Transition Analysis Report

Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Main Conversation Graph

### Nodes:
"""

        # Add main graph nodes
        for node_id in main_graph_info.nodes:
            report_content += f"- **{node_id}**\n"

        report_content += f"""
### Edges:
"""

        # Add main graph edges
        for edge in main_graph_info.edges:
            report_content += f"- {edge.source} → {edge.target}\n"

        report_content += f"""
### Graph Properties:
- **Total Nodes**: {len(main_graph_info.nodes)}
- **Total Edges**: {len(main_graph_info.edges)}
- **Graph Type**: Main conversation router

## Package Subgraph

### Nodes:
"""

        # Add package subgraph nodes
        for node_id in package_graph_info.nodes:
            report_content += f"- **{node_id}**\n"

        report_content += f"""
### Edges:
"""

        # Add package subgraph edges
        for edge in package_graph_info.edges:
            report_content += f"- {edge.source} → {edge.target}\n"

        report_content += f"""
### Graph Properties:
- **Total Nodes**: {len(package_graph_info.nodes)}
- **Total Edges**: {len(package_graph_info.edges)}
- **Graph Type**: Dynamic looping subgraph

### Conditional Edges:
"""

        # Add conditional edges information
        conditional_count = 0
        for edge in package_graph_info.edges:
            if hasattr(edge, 'condition') and edge.condition:
                report_content += f"- {edge.source} → (condition) → {edge.target}\n"
                conditional_count += 1

        if conditional_count == 0:
            report_content += "- Multiple conditional routing edges present\n"

        report_content += f"""
## Graph Characteristics

### Main Graph:
- **Total Nodes**: {len(main_graph_info.nodes)}
- **Total Edges**: {len(main_graph_info.edges)}
- **Graph Type**: Main conversation router
- **Parallelism**: Sequential intent-based routing

### Package Subgraph:
- **Total Nodes**: {len(package_graph_info.nodes)}
- **Total Edges**: {len(package_graph_info.edges)}
- **Graph Type**: Dynamic looping subgraph
- **Parallelism**: Human-in-the-loop with conditional branching

## Technical Implementation

### Async Operations:
- ✅ OpenAI API calls (AsyncOpenAI)
- ✅ SQLite database operations (asyncio.to_thread)
- ✅ Intent classification
- ✅ LLM-based SQL generation

### State Management:
- **Main State**: GraphState with conversation context
- **Subgraph State**: MedicalPackageSubgraphState with 4 required fields
- **Persistence**: In-memory conversation states (production: database)

### Key Features:
- **Dynamic Looping**: Package subgraph loops until all 4 fields captured
- **LLM Integration**: GPT-4 for SQL generation, GPT-3.5 for intent classification
- **Non-blocking**: All operations use async patterns
- **Error Handling**: Fallback mechanisms for LLM and database failures
- **Logging**: Bangkok timezone (UTC+7) with structured logging

## Files Generated:
- Main conversation graph PNG and Mermaid source
- Package subgraph PNG and Mermaid source
- ASCII representations for both graphs
- This analysis report

---
*Generated by LangGraph Native Visualization Script*
"""

        with open(report_path, "w") as f:
            f.write(report_content)

        print(f"   ✅ Analysis report saved: {report_path}")

    except Exception as e:
        print(f"   ❌ Error creating analysis report: {e}")
        import traceback
        traceback.print_exc()

def print_graph_summary():
    """Print a summary of the graph structure to console"""

    print("\n📊 Graph Structure Summary:")
    print("=" * 50)

    try:
        from app.services.conversation_graph import create_conversation_graph
        from app.flows.package_flow import create_package_subgraph

        # Main graph summary
        main_graph = create_conversation_graph()
        main_info = main_graph.get_graph()

        print(f"🔹 Main Conversation Graph:")
        print(f"   • Nodes: {len(main_info.nodes)}")
        print(f"   • Edges: {len(main_info.edges)}")

        print(f"   • Node List: {', '.join(main_info.nodes.keys())}")

        # Package subgraph summary
        package_graph = create_package_subgraph()
        package_info = package_graph.get_graph()

        print(f"\n🔹 Package Subgraph:")
        print(f"   • Nodes: {len(package_info.nodes)}")
        print(f"   • Edges: {len(package_info.edges)}")

        print(f"   • Node List: {', '.join(package_info.nodes.keys())}")

        # Show conditional edges
        conditional_edges = [edge for edge in package_info.edges if hasattr(edge, 'condition')]
        if conditional_edges:
            print(f"   • Conditional Edges: {len(conditional_edges)}")
        else:
            print(f"   • Conditional Edges: Present (routing logic)")

    except Exception as e:
        print(f"❌ Error getting graph summary: {e}")

def main():
    """Main function to create all visualizations"""

    print("🚀 LangGraph Native Visualization Generator")
    print("=" * 60)

    # Create visualizations
    output_dir = create_langgraph_visualizations()

    # Create analysis report
    create_graph_analysis_report(output_dir)

    # Print summary
    print_graph_summary()

    print(f"\n🎉 LangGraph Native Visualizations Complete!")
    print(f"📁 Output directory: {output_dir.absolute()}")
    print(f"📋 Files generated:")

    # List generated files
    for file_path in sorted(output_dir.glob("*")):
        file_size = file_path.stat().st_size
        print(f"   • {file_path.name} ({file_size:,} bytes)")

    print(f"\n💡 Usage Tips:")
    print(f"   • PNG files can be viewed directly")
    print(f"   • .mmd files can be opened in Mermaid editors")
    print(f"   • .txt files contain ASCII representations")
    print(f"   • .md file contains detailed analysis")

if __name__ == "__main__":
    main()
