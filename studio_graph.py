"""
LangGraph Studio entry point for the Receptionist Chatbot
This file provides a clean interface for LangGraph Studio to load the conversation graph.
"""

from app.services.conversation_graph import create_conversation_graph

# Export the graph for Studio
graph = create_conversation_graph()

# For debugging and testing
if __name__ == "__main__":
    print("Graph created successfully for LangGraph Studio")
    print(f"Graph type: {type(graph)}")
    print(f"Graph nodes: {list(graph.nodes.keys()) if hasattr(graph, 'nodes') else 'N/A'}")
