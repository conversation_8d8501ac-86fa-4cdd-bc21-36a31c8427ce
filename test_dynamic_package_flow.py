"""
Test script for the new dynamic package flow with LLM-based SQL generation
"""

import asyncio
from app.flows.package_flow import flow_package_start
from app.models.schemas import ConversationState
from datetime import datetime

async def test_dynamic_package_flow():
    """Test the dynamic package flow with 4 required fields and LLM SQL generation"""
    
    print("🧪 Testing Dynamic Package Flow with LLM SQL Generation...")
    print("=" * 70)
    
    test_cases = [
        {
            "input": "I'm looking for botox treatments for beauty and confidence for adults",
            "description": "Complete input - all 4 fields in one message",
            "expected_fields": ["category", "goal_tags", "description_short", "target_group"]
        },
        {
            "input": "I want aesthetic treatments",
            "description": "Partial input - category only",
            "expected_fields": ["category"]
        },
        {
            "input": "I need vaccines for children",
            "description": "Partial input - category and target_group",
            "expected_fields": ["category", "target_group"]
        },
        {
            "input": "Health checkup for prevention and early screening",
            "description": "Partial input - category and goal_tags",
            "expected_fields": ["category", "goal_tags"]
        },
        {
            "input": "Beauty treatments for women to boost confidence",
            "description": "Multiple fields - goal_tags and target_group",
            "expected_fields": ["goal_tags", "target_group"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['description']}")
        print(f"   Input: '{test_case['input']}'")
        print(f"   Expected Fields: {test_case['expected_fields']}")
        
        # Create conversation state
        state = ConversationState(
            session_id=f"test_dynamic_{i}",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        try:
            # Run the package flow
            result = await flow_package_start(state, test_case["input"])
            
            print(f"   ✅ Response: {result['response'][:100]}...")
            print(f"   ✅ Completed: {result['completed']}")
            
            # Check if this was a complete interaction or needs more input
            if result['completed']:
                print(f"   🎉 Flow completed successfully!")
            else:
                print(f"   🔄 Flow needs more input (as expected for partial cases)")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎉 Dynamic package flow testing completed!")

async def test_llm_sql_generation():
    """Test LLM SQL generation specifically"""
    
    print("\n🤖 Testing LLM SQL Generation...")
    print("=" * 50)
    
    from app.flows.package_flow import build_query_node, MedicalPackageSubgraphState
    
    # Test with complete extracted slots
    test_state: MedicalPackageSubgraphState = {
        "messages": [],
        "user_input": "botox for beauty and confidence for adults",
        "extracted_slots": {
            "category": "aesthetic",
            "goal_tags": "beauty, confidence", 
            "description_short": "botox",
            "target_group": "adults"
        },
        "missing_fields": [],
        "query_results": [],
        "generated_sql": "",
        "response": "",
        "completed": False,
        "iteration_count": 1
    }
    
    try:
        # Test SQL generation
        result_state = await build_query_node(test_state)
        generated_sql = result_state.get("generated_sql", "")
        
        print(f"📝 Input Slots: {test_state['extracted_slots']}")
        print(f"🔍 Generated SQL: {generated_sql}")
        
        if generated_sql and "SELECT" in generated_sql.upper():
            print("   ✅ SQL generation successful!")
        else:
            print("   ❌ SQL generation failed or incomplete")
            
    except Exception as e:
        print(f"   ❌ Error in SQL generation: {e}")

async def test_field_extraction():
    """Test the 4-field extraction logic"""
    
    print("\n🔍 Testing Field Extraction...")
    print("=" * 40)
    
    from app.flows.package_flow import extract_slots_node, MedicalPackageSubgraphState
    
    test_inputs = [
        {
            "input": "I want botox for beauty and confidence for adults",
            "expected": {
                "category": "aesthetic",
                "goal_tags": "beauty, confidence",
                "description_short": "botox",
                "target_group": "adults"
            }
        },
        {
            "input": "Vaccines for children to prevent diseases",
            "expected": {
                "category": "vaccine", 
                "goal_tags": "prevention",
                "description_short": "vaccine",
                "target_group": "children"
            }
        },
        {
            "input": "Health checkup for seniors for early screening",
            "expected": {
                "category": "checkup",
                "goal_tags": "screening",
                "description_short": "checkup",
                "target_group": "seniors"
            }
        }
    ]
    
    for i, test in enumerate(test_inputs, 1):
        print(f"\n📝 Extraction Test {i}:")
        print(f"   Input: '{test['input']}'")
        
        test_state: MedicalPackageSubgraphState = {
            "messages": [],
            "user_input": test["input"],
            "extracted_slots": {},
            "missing_fields": ["category", "goal_tags", "description_short", "target_group"],
            "query_results": [],
            "generated_sql": "",
            "response": "",
            "completed": False,
            "iteration_count": 1
        }
        
        try:
            result_state = await extract_slots_node(test_state)
            extracted = result_state["extracted_slots"]
            missing = result_state["missing_fields"]
            
            print(f"   🔍 Extracted: {extracted}")
            print(f"   ❓ Missing: {missing}")
            
            # Check if extraction matches expectations
            matches = 0
            for field, expected_value in test["expected"].items():
                if field in extracted:
                    matches += 1
                    print(f"   ✅ {field}: {extracted[field]}")
                else:
                    print(f"   ❌ {field}: Not extracted (expected: {expected_value})")
            
            print(f"   📊 Extraction Score: {matches}/{len(test['expected'])}")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")

async def run_all_tests():
    """Run all dynamic package flow tests"""
    print("🚀 Starting Dynamic Package Flow Tests")
    print("=" * 70)
    
    await test_field_extraction()
    await test_llm_sql_generation() 
    await test_dynamic_package_flow()
    
    print("\n" + "=" * 70)
    print("🎉 ALL DYNAMIC PACKAGE FLOW TESTS COMPLETED!")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
