"""
Simple verification script for the merged validate_and_check_completion node
"""

def verify_merged_implementation():
    """Verify that the merge was successful"""
    
    print("🔍 Verifying Merged Validation + Completion Implementation")
    print("=" * 60)
    
    # Check package_flow.py
    print("\n📄 Checking app/flows/package_flow.py...")
    
    try:
        with open("app/flows/package_flow.py", "r") as f:
            content = f.read()
        
        # Check for new merged node
        has_merged_node = "validate_and_check_completion_node" in content
        print(f"   ✅ validate_and_check_completion_node: {'Found' if has_merged_node else 'Missing'}")
        
        # Check old nodes are removed
        has_old_validate = "validate_extraction_node" in content and "def validate_extraction_node" in content
        has_old_check = "check_completion_node" in content and "def check_completion_node" in content
        
        print(f"   ✅ validate_extraction_node removed: {'No' if has_old_validate else 'Yes'}")
        print(f"   ✅ check_completion_node removed: {'No' if has_old_check else 'Yes'}")
        
        # Check new routing function
        has_merged_routing = "should_request_human_feedback_or_continue" in content
        print(f"   ✅ should_request_human_feedback_or_continue: {'Found' if has_merged_routing else 'Missing'}")
        
        # Check subgraph uses new node
        has_new_subgraph_node = 'workflow.add_node("validate_and_check_completion"' in content
        print(f"   ✅ Subgraph uses merged node: {'Yes' if has_new_subgraph_node else 'No'}")
        
        # Check node count comment
        has_6_nodes_comment = "6 nodes total" in content
        print(f"   ✅ Updated to 6 nodes comment: {'Yes' if has_6_nodes_comment else 'No'}")
        
        package_flow_success = all([
            has_merged_node,
            not has_old_validate,
            not has_old_check,
            has_merged_routing,
            has_new_subgraph_node,
            has_6_nodes_comment
        ])
        
    except Exception as e:
        print(f"   ❌ Error reading package_flow.py: {e}")
        package_flow_success = False
    
    # Check conversation_graph.py
    print("\n📄 Checking app/services/conversation_graph.py...")
    
    try:
        with open("app/services/conversation_graph.py", "r") as f:
            content = f.read()
        
        # Check imports
        imports_merged_node = "validate_and_check_completion_node" in content
        imports_merged_routing = "should_request_human_feedback_or_continue" in content
        print(f"   ✅ Imports merged node: {'Yes' if imports_merged_node else 'No'}")
        print(f"   ✅ Imports merged routing: {'Yes' if imports_merged_routing else 'No'}")
        
        # Check adapter method
        has_merged_adapter = "_validate_and_check_completion_adapter" in content
        print(f"   ✅ Has merged adapter: {'Yes' if has_merged_adapter else 'No'}")
        
        # Check old adapters removed
        has_old_validate_adapter = "_validate_extraction_adapter" in content and "def _validate_extraction_adapter" in content
        has_old_check_adapter = "_check_completion_adapter" in content and "def _check_completion_adapter" in content
        
        print(f"   ✅ Old validate adapter removed: {'No' if has_old_validate_adapter else 'Yes'}")
        print(f"   ✅ Old check adapter removed: {'No' if has_old_check_adapter else 'Yes'}")
        
        # Check subgraph creation
        uses_merged_in_subgraph = '"validate_and_check_completion"' in content
        print(f"   ✅ Subgraph uses merged node: {'Yes' if uses_merged_in_subgraph else 'No'}")
        
        # Check routing adapter
        has_merged_routing_adapter = "_should_request_human_feedback_or_continue_adapter" in content
        print(f"   ✅ Has merged routing adapter: {'Yes' if has_merged_routing_adapter else 'No'}")
        
        conversation_graph_success = all([
            imports_merged_node,
            imports_merged_routing,
            has_merged_adapter,
            not has_old_validate_adapter,
            not has_old_check_adapter,
            uses_merged_in_subgraph,
            has_merged_routing_adapter
        ])
        
    except Exception as e:
        print(f"   ❌ Error reading conversation_graph.py: {e}")
        conversation_graph_success = False
    
    # Summary
    print(f"\n📊 Verification Summary:")
    print(f"   📄 package_flow.py: {'✅ Pass' if package_flow_success else '❌ Fail'}")
    print(f"   📄 conversation_graph.py: {'✅ Pass' if conversation_graph_success else '❌ Fail'}")
    
    overall_success = package_flow_success and conversation_graph_success
    
    if overall_success:
        print(f"\n🎉 SUCCESS: Merged validation+completion implementation verified!")
        print(f"\n💡 Key Changes Confirmed:")
        print(f"   ✅ validate_extraction_node + check_completion_node → validate_and_check_completion_node")
        print(f"   ✅ Package subgraph reduced from 7 to 6 nodes")
        print(f"   ✅ Three-way routing: request_feedback | continue | build_query")
        print(f"   ✅ All adapter methods updated")
        print(f"   ✅ Subgraph creation updated")
        print(f"   ✅ Import statements cleaned up")
        
        print(f"\n🔄 New Flow Architecture:")
        print(f"   extract_slots → validate_and_check_completion → [request_clarification|extract_slots|build_query]")
        print(f"                                                    ↓")
        print(f"                                                human_feedback")
        print(f"                                                    ↓")
        print(f"                                                extract_slots")
        
    else:
        print(f"\n❌ FAILURE: Some verification checks failed!")
        print(f"   Please review the implementation and fix any issues.")
    
    return overall_success

def check_node_architecture():
    """Check the expected node architecture"""
    
    print(f"\n🏗️ Expected Node Architecture After Merge:")
    print(f"=" * 50)
    
    expected_nodes = [
        "extract_slots (merged: input + extraction)",
        "validate_and_check_completion (merged: validation + completion)",
        "request_clarification (HIL)",
        "human_feedback (HIL)",
        "build_query",
        "validate_query",
        "final_answer"
    ]
    
    print(f"📊 Package Subgraph Nodes (6 total):")
    for i, node in enumerate(expected_nodes, 1):
        print(f"   {i}. {node}")
    
    print(f"\n🔄 Routing Logic:")
    print(f"   validate_and_check_completion decides:")
    print(f"   • request_feedback → if validation issues (low confidence, contradictions)")
    print(f"   • continue → if missing fields but no validation issues")
    print(f"   • build_query → if all fields complete and no validation issues")
    
    print(f"\n🎯 Benefits of Merge:")
    print(f"   ✅ Reduced complexity (6 nodes instead of 7)")
    print(f"   ✅ Single decision point for validation + completion")
    print(f"   ✅ More efficient execution path")
    print(f"   ✅ Preserved all HIL functionality")
    print(f"   ✅ Cleaner graph visualization")

if __name__ == "__main__":
    print("🚀 Starting Merged Node Verification...")
    
    success = verify_merged_implementation()
    check_node_architecture()
    
    if success:
        print(f"\n✅ Verification complete - merge successful!")
    else:
        print(f"\n❌ Verification failed - please check implementation!")
