# Unified Receptionist Chatbot State Graph Visualization

Generated: 2025-05-30 02:22:53

## Overview

This directory contains a **unified state graph visualization** that shows the complete receptionist chatbot architecture in a single diagram. Unlike separate visualizations, this unified graph includes both the main conversation flow and the detailed package subgraph as embedded nodes.

## Files

### 📊 Visualization Files
- **`unified_conversation_graph_TIMESTAMP.png`** - High-quality PNG image (recommended for viewing)
- **`unified_conversation_graph_TIMESTAMP.mmd`** - Mermaid source code (editable)
- **`unified_conversation_graph_TIMESTAMP.txt`** - ASCII text representation (terminal-friendly)

### 📋 Documentation
- **`README.md`** - This documentation file

## Graph Structure

### 📈 Statistics
- **Total Nodes**: 13
- **Total Edges**: 18
- **Main Flow Nodes**: 5 (intent routing)
- **Package Subgraph Nodes**: 6 (dynamic loop)
- **Conditional Edges**: 2 (routing logic)

### 🔄 Main Conversation Flow

```mermaid
graph TD;
    __start__ --> classify_intent
    classify_intent -.booking.-> flow_booking_start
    classify_intent -.greeting.-> flow_greeting_start
    classify_intent -.info.-> flow_info_start
    classify_intent -.unknown.-> flow_unknown_start
    classify_intent -.package.-> package_await_input
```

**Intent Classification Routes:**
- `greeting` → Simple greeting response
- `booking` → Appointment booking flow
- `info` → Information about services/hours
- `unknown` → Fallback response
- `package` → **Dynamic package subgraph** (detailed below)

### 📦 Package Subgraph (Embedded)

The package intent routes to a sophisticated **dynamic looping subgraph** that captures 4 required fields:

```mermaid
graph TD;
    package_await_input --> package_extract_slots
    package_extract_slots --> package_check_completion
    package_check_completion -.continue.-> package_await_input
    package_check_completion -.build_query.-> package_build_query
    package_build_query --> package_validate_query
    package_validate_query -.retry.-> package_build_query
    package_validate_query -.proceed.-> package_final_answer
    package_final_answer --> __end__
```

#### 🔄 Dynamic Loop Behavior

1. **`package_await_input`** - Human-in-the-loop input collection
   - Context-aware prompting
   - Shows missing fields to user
   - Tracks iteration count

2. **`package_extract_slots`** - Field extraction from user input
   - Extracts 4 required fields: `category`, `goal_tags`, `description_short`, `target_group`
   - Uses keyword matching and pattern recognition
   - Updates missing fields list

3. **`package_check_completion`** - Completion validation
   - **If missing fields** → `continue` → Loop back to `package_await_input`
   - **If all fields captured** → `build_query` → Proceed to SQL generation

4. **`package_build_query`** - LLM-powered SQL generation
   - Uses **GPT-4** for dynamic SQL query creation
   - JSON response format: `{"query": "SELECT ..."}`
   - Async non-blocking operation

5. **`package_validate_query`** - Query execution and validation
   - **If SQL invalid** → `retry` → Loop back to `package_build_query`
   - **If SQL valid** → `proceed` → Execute query and format results

6. **`package_final_answer`** - Result formatting and response
   - Formats database results into natural language
   - Provides package recommendations
   - Completes the conversation

## Key Features Visualized

### ✅ **Intent-Based Routing**
- Single entry point through `classify_intent`
- 5-way conditional routing based on user intent
- OpenAI GPT-3.5 + rule-based classification

### ✅ **Dynamic Looping Subgraph**
- **Human-in-the-loop** design with `package_await_input`
- **Progressive field collection** until all 4 fields captured
- **Intelligent prompting** that asks for specific missing fields

### ✅ **LLM Integration Points**
- **Intent Classification**: GPT-3.5 for intent detection
- **SQL Generation**: GPT-4 for dynamic query creation
- **All async operations** to prevent blocking

### ✅ **Error Handling & Retry Logic**
- **Query retry mechanism** if SQL generation fails
- **Maximum iteration limits** to prevent infinite loops
- **Fallback responses** for error conditions

### ✅ **Database Integration**
- **Async SQLite operations** using `asyncio.to_thread()`
- **Real medical package database** with 200+ packages
- **Non-blocking database access** for concurrent users

## Technical Implementation

### 🔧 **State Management**
- **Unified State**: `UnifiedGraphState` handles both main flow and subgraph
- **Field Tracking**: Maintains extracted slots and missing fields
- **Iteration Control**: Prevents infinite loops with counters

### 🔧 **Async Operations**
- **OpenAI Calls**: `AsyncOpenAI` for non-blocking LLM operations
- **Database Access**: `asyncio.to_thread()` for SQLite operations
- **Concurrent Processing**: Multiple users can interact simultaneously

### 🔧 **4 Required Fields System**
1. **`category`** - Type of medical service (aesthetic, vaccine, checkup, wellness)
2. **`goal_tags`** - User goals (beauty, health, prevention, screening, confidence)
3. **`description_short`** - Specific treatment description (botox, vaccine, checkup)
4. **`target_group`** - Demographics (adults, children, seniors, women, men)

## Usage Examples

### 📝 **Complete Input (No Loop)**
```
User: "I want botox treatments for beauty and confidence for adults"
Flow: classify_intent → package_await_input → package_extract_slots → 
      package_check_completion → package_build_query → package_validate_query → 
      package_final_answer → END
```

### 📝 **Partial Input (With Loop)**
```
User: "I want aesthetic treatments"
Flow: classify_intent → package_await_input → package_extract_slots → 
      package_check_completion → package_await_input (loop) → ...
      (continues until all 4 fields captured)
```

## Benefits of Unified Visualization

### ✅ **Complete Picture**
- Shows entire system architecture in one diagram
- No need to reference multiple separate diagrams
- Clear understanding of how components interact

### ✅ **LangGraph Native**
- Generated using LangGraph's built-in visualization utilities
- Accurate representation of actual graph structure
- Always in sync with implementation

### ✅ **Professional Quality**
- High-resolution PNG for presentations
- Editable Mermaid source for modifications
- ASCII version for documentation

### ✅ **Multiple Formats**
- **PNG**: For presentations and visual documentation
- **Mermaid**: For editing and customization
- **ASCII**: For terminal viewing and text documentation

## Viewing Instructions

### 🖼️ **PNG Image**
- Open `unified_conversation_graph_TIMESTAMP.png` in any image viewer
- High-quality visualization suitable for presentations
- Shows complete flow with clear node relationships

### ✏️ **Mermaid Source**
- Open `unified_conversation_graph_TIMESTAMP.mmd` in:
  - [Mermaid Live Editor](https://mermaid.live/)
  - VS Code with Mermaid extension
  - Any Markdown editor with Mermaid support

### 📟 **ASCII Text**
- View `unified_conversation_graph_TIMESTAMP.txt` in terminal:
  ```bash
  cat unified_conversation_graph_TIMESTAMP.txt
  ```
- Text-based representation for documentation

## Integration with LangGraph Studio

This unified graph is fully compatible with **LangGraph Studio** and can be used for:
- **Visual debugging** of conversation flows
- **Step-by-step execution** monitoring
- **State inspection** at each node
- **Performance analysis** of the dynamic loop

---

*Generated by LangGraph Native Visualization with Unified Subgraph Integration*
