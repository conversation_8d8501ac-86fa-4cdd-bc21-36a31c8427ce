"""
Enhanced visualization script with better layout and more details
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, Circle
import numpy as np
from datetime import datetime

def create_enhanced_diagram():
    """Create an enhanced state transition diagram with better layout"""
    
    # Create figure with larger size for more detail
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(24, 16))
    
    # Main flow diagram (left side)
    create_main_flow_diagram(ax1)
    
    # Package subgraph detail (right side)
    create_package_subgraph_detail(ax2)
    
    # Overall title
    fig.suptitle('Receptionist Chatbot - Complete State Transition Architecture', 
                fontsize=24, fontweight='bold', y=0.95)
    
    plt.tight_layout()
    return fig

def create_main_flow_diagram(ax):
    """Create the main conversation flow diagram"""
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 16)
    ax.axis('off')
    ax.set_title('Main Conversation Flow', fontsize=18, fontweight='bold', pad=20)
    
    # Color scheme
    colors = {
        'start': '#4CAF50',
        'process': '#2196F3', 
        'decision': '#FF9800',
        'subgraph': '#9C27B0',
        'end': '#F44336'
    }
    
    # Nodes with positions
    nodes = [
        ('START', 6, 15, colors['start'], 'circle'),
        ('Intent\nClassification', 6, 13, colors['process'], 'rect'),
        ('Route Intent', 6, 11, colors['decision'], 'diamond'),
        ('Greeting\nFlow', 2, 9, colors['process'], 'rect'),
        ('Booking\nFlow', 4, 9, colors['process'], 'rect'),
        ('Info\nFlow', 6, 9, colors['process'], 'rect'),
        ('Package\nSubgraph', 8, 9, colors['subgraph'], 'rect'),
        ('Unknown\nFlow', 10, 9, colors['process'], 'rect'),
        ('END', 6, 7, colors['end'], 'circle')
    ]
    
    # Draw nodes
    for name, x, y, color, shape in nodes:
        if shape == 'circle':
            node = Circle((x, y), 0.8, facecolor=color, edgecolor='black', linewidth=2)
        elif shape == 'diamond':
            node = patches.RegularPolygon((x, y), 4, radius=1, orientation=np.pi/4,
                                        facecolor=color, edgecolor='black', linewidth=2)
        else:  # rectangle
            node = FancyBboxPatch((x-1, y-0.5), 2, 1, boxstyle="round,pad=0.1",
                                facecolor=color, edgecolor='black', linewidth=2)
        
        ax.add_patch(node)
        text_color = 'white' if color != colors['start'] else 'black'
        ax.text(x, y, name, ha='center', va='center', fontweight='bold', 
                color=text_color, fontsize=10)
    
    # Draw connections
    connections = [
        (6, 14.2, 6, 13.8),  # START -> Intent Classification
        (6, 12.5, 6, 12),    # Intent Classification -> Route Intent
        (5.3, 10.3, 2.7, 9.7),  # Route -> Greeting
        (5.6, 10.6, 4.4, 9.4),  # Route -> Booking
        (6, 10, 6, 9.5),     # Route -> Info
        (6.4, 10.6, 7.6, 9.4),  # Route -> Package
        (6.7, 10.3, 9.3, 9.7),  # Route -> Unknown
        (2, 8.5, 5.2, 7.7),  # Greeting -> END
        (4, 8.5, 5.6, 7.6),  # Booking -> END
        (6, 8.5, 6, 7.8),    # Info -> END
        (8, 8.5, 6.8, 7.6),  # Package -> END
        (10, 8.5, 6.8, 7.7), # Unknown -> END
    ]
    
    for x1, y1, x2, y2 in connections:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', color='black', lw=2))
    
    # Add intent labels
    intent_labels = [
        ('greeting', 3.5, 10.5),
        ('booking', 4.8, 10.8),
        ('info', 6, 10.3),
        ('package', 7.2, 10.8),
        ('unknown', 8.5, 10.5)
    ]
    
    for label, x, y in intent_labels:
        ax.text(x, y, label, ha='center', va='center', fontsize=9,
                bbox=dict(boxstyle="round,pad=0.2", facecolor='yellow', alpha=0.7))
    
    # Add technology annotations
    ax.text(6, 12.2, 'OpenAI GPT-3.5 + Rule-based', ha='center', va='center', 
            fontsize=8, style='italic', color='blue')
    
    ax.text(8, 8.2, 'Dynamic Subgraph\nwith LLM SQL', ha='center', va='center',
            fontsize=8, style='italic', color='purple',
            bbox=dict(boxstyle="round,pad=0.2", facecolor='lavender', alpha=0.8))

def create_package_subgraph_detail(ax):
    """Create detailed package subgraph visualization"""
    
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 16)
    ax.axis('off')
    ax.set_title('Package Subgraph - Dynamic Loop with LLM SQL Generation', 
                fontsize=18, fontweight='bold', pad=20)
    
    # Colors for different node types
    colors = {
        'human': '#FF9800',      # Orange
        'extract': '#2196F3',    # Blue  
        'decision': '#F44336',   # Red
        'llm': '#9C27B0',       # Purple
        'db': '#4CAF50',        # Green
        'output': '#607D8B'     # Blue Grey
    }
    
    # Detailed nodes with exact positions
    detailed_nodes = [
        ('Entry', 6, 15, colors['human'], 'User Input\n"botox for women"'),
        ('await_input', 2, 13, colors['human'], 'Await Input\n(Context-Aware\nPrompting)'),
        ('extract_slots', 6, 13, colors['extract'], 'Extract Slots\n• category\n• goal_tags\n• description_short\n• target_group'),
        ('check_completion', 10, 13, colors['decision'], 'Check\nCompletion\n(4 fields?)'),
        ('build_query', 6, 10, colors['llm'], 'Build Query\n(GPT-4 SQL\nGeneration)'),
        ('validate_query', 10, 10, colors['db'], 'Validate Query\n(Async SQLite\nExecution)'),
        ('final_answer', 6, 7, colors['output'], 'Final Answer\n(Formatted\nResults)')
    ]
    
    # Draw detailed nodes
    for name, x, y, color, description in detailed_nodes:
        if name == 'check_completion':
            # Diamond for decision
            node = patches.RegularPolygon((x, y), 4, radius=1.2, orientation=np.pi/4,
                                        facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(node)
            ax.text(x, y, 'Check\nCompletion', ha='center', va='center', 
                   fontweight='bold', color='white', fontsize=9)
        else:
            # Rectangle for other nodes
            height = 1.5 if name in ['extract_slots', 'await_input'] else 1.2
            node = FancyBboxPatch((x-1.5, y-height/2), 3, height, boxstyle="round,pad=0.1",
                                facecolor=color, edgecolor='black', linewidth=2)
            ax.add_patch(node)
            
            text_color = 'white' if color != colors['db'] else 'black'
            ax.text(x, y, name.replace('_', '\n'), ha='center', va='center',
                   fontweight='bold', color=text_color, fontsize=10)
        
        # Add description below
        if name != 'Entry':
            ax.text(x, y-2, description, ha='center', va='center', fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    # Draw flow arrows
    flow_arrows = [
        (6, 14.2, 2.8, 13.8, 'black', 'start'),
        (3.5, 13, 4.5, 13, 'black', 'normal'),
        (7.5, 13, 8.8, 13, 'black', 'normal'),
        (10, 11.8, 7.5, 10.8, 'green', 'all fields\ncaptured'),
        (7.5, 10, 8.5, 10, 'black', 'normal'),
        (8.5, 10, 7.5, 7.8, 'black', 'normal')
    ]
    
    for x1, y1, x2, y2, color, label in flow_arrows:
        ax.annotate('', xy=(x2, y2), xytext=(x1, y1),
                   arrowprops=dict(arrowstyle='->', color=color, lw=2))
        
        if label not in ['start', 'normal']:
            mid_x, mid_y = (x1 + x2) / 2, (y1 + y2) / 2
            ax.text(mid_x, mid_y + 0.3, label, ha='center', va='center', fontsize=8,
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='lightgreen', alpha=0.8))
    
    # Loop back arrow (missing fields)
    ax.annotate('', xy=(2, 12.2), xytext=(9.2, 12.2),
               arrowprops=dict(arrowstyle='->', color='red', lw=2,
                             connectionstyle="arc3,rad=-0.3"))
    ax.text(5.5, 11.5, 'missing fields\n(continue loop)', ha='center', va='center', fontsize=8,
           bbox=dict(boxstyle="round,pad=0.2", facecolor='lightcoral', alpha=0.8))
    
    # Retry arrow for query validation
    ax.annotate('', xy=(10.8, 9.2), xytext=(10.8, 10.8),
               arrowprops=dict(arrowstyle='->', color='orange', lw=2,
                             connectionstyle="arc3,rad=0.5"))
    ax.text(11.5, 10, 'retry\nquery', ha='center', va='center', fontsize=8,
           bbox=dict(boxstyle="round,pad=0.2", facecolor='lightyellow', alpha=0.8))
    
    # Add technical details
    tech_details = [
        (2, 11, 'Human-in-the-loop\nContext-aware prompts\nMissing field detection'),
        (6, 11, 'LLM-powered SQL\nGPT-4 model\nJSON response format'),
        (10, 8, 'Async SQLite\nasyncio.to_thread()\nNon-blocking execution')
    ]
    
    for x, y, details in tech_details:
        ax.text(x, y, details, ha='center', va='center', fontsize=8,
               bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    # Add field requirements
    ax.text(6, 5, 'Required Fields (All 4 Must Be Captured):', ha='center', va='center',
           fontsize=12, fontweight='bold')
    
    fields = [
        '1. category (aesthetic, vaccine, checkup, wellness)',
        '2. goal_tags (beauty, health, prevention, screening, etc.)',
        '3. description_short (specific treatment/service)',
        '4. target_group (adults, children, seniors, women, men)'
    ]
    
    for i, field in enumerate(fields):
        ax.text(6, 4.2 - i*0.4, field, ha='center', va='center', fontsize=10,
               bbox=dict(boxstyle="round,pad=0.2", facecolor='lightyellow', alpha=0.8))

def save_enhanced_visualization():
    """Create and save the enhanced visualization"""
    
    print("🎨 Creating Enhanced State Transition Visualization...")
    
    # Create the enhanced diagram
    fig = create_enhanced_diagram()
    
    # Save with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"enhanced_chatbot_architecture_{timestamp}.png"
    pdf_filename = f"enhanced_chatbot_architecture_{timestamp}.pdf"
    
    # Save high-quality PNG
    fig.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
    
    # Save vector PDF
    fig.savefig(pdf_filename, format='pdf', bbox_inches='tight', facecolor='white')
    
    print(f"✅ Enhanced visualization saved as: {filename}")
    print(f"✅ Vector version saved as: {pdf_filename}")
    print(f"📊 Features:")
    print("   • Split layout: Main flow + Detailed subgraph")
    print("   • Technical annotations and specifications")
    print("   • Color-coded node types by functionality")
    print("   • Detailed field requirements")
    print("   • Loop and retry mechanisms clearly shown")
    print("   • LLM and async operation highlights")
    
    plt.close(fig)
    return filename, pdf_filename

if __name__ == "__main__":
    try:
        png_file, pdf_file = save_enhanced_visualization()
        
        print(f"\n🎉 Enhanced visualization completed!")
        print(f"📁 Files created:")
        print(f"   • {png_file} (24x16 inches, 300 DPI)")
        print(f"   • {pdf_file} (Vector format)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
