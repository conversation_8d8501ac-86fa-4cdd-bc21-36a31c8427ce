"""
Test script for the refactored package subgraph implementation
This script tests the new compiled subgraph approach for flow_package_start
"""

from datetime import datetime
from pathlib import Path

def test_refactored_package_subgraph():
    """Test the refactored package subgraph implementation"""
    
    print("🔧 Testing Refactored Package Subgraph...")
    print("=" * 60)
    
    try:
        # Import the conversation graph
        from app.services.conversation_graph import ConversationGraph
        
        print("📊 Creating conversation graph with compiled package subgraph...")
        
        # Create the conversation graph
        conversation_graph = ConversationGraph()
        
        # Get graph information
        graph_info = conversation_graph.graph.get_graph()
        
        print(f"✅ Graph created successfully!")
        print(f"   • Total Nodes: {len(graph_info.nodes)}")
        print(f"   • Total Edges: {len(graph_info.edges)}")
        
        # List all nodes
        print(f"\n📋 All Nodes:")
        for node_name in sorted(graph_info.nodes.keys()):
            if node_name == "flow_package_start":
                node_type = "Compiled Subgraph"
            elif node_name.startswith("flow_"):
                node_type = "Main Flow"
            elif node_name in ["__start__", "__end__"]:
                node_type = "System"
            else:
                node_type = "Core"
            print(f"   • {node_name} ({node_type})")
        
        # Check if package subgraph is properly integrated
        package_node = graph_info.nodes.get("flow_package_start")
        if package_node:
            print(f"\n🎯 Package Subgraph Integration:")
            print(f"   ✅ flow_package_start node found")
            print(f"   ✅ Node type: {type(package_node)}")
            
            # Check if it's a compiled subgraph
            if hasattr(package_node, 'get_graph'):
                subgraph_info = package_node.get_graph()
                print(f"   ✅ Subgraph detected with {len(subgraph_info.nodes)} internal nodes")
                print(f"   📋 Subgraph nodes: {list(subgraph_info.nodes.keys())}")
            else:
                print(f"   ⚠️  Not a compiled subgraph")
        else:
            print(f"\n❌ flow_package_start node not found!")
        
        # Create output directory
        output_dir = Path("refactored_subgraph_test")
        output_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate visualizations
        print(f"\n🎨 Generating visualizations...")
        
        try:
            # Generate Mermaid PNG
            mermaid_png = conversation_graph.graph.get_graph().draw_mermaid_png()
            png_path = output_dir / f"refactored_graph_{timestamp}.png"
            
            with open(png_path, "wb") as f:
                f.write(mermaid_png)
            
            print(f"   ✅ PNG saved: {png_path}")
            
        except Exception as e:
            print(f"   ❌ Error creating PNG: {e}")
        
        try:
            # Generate Mermaid source
            mermaid_source = conversation_graph.graph.get_graph().draw_mermaid()
            mermaid_path = output_dir / f"refactored_graph_{timestamp}.mmd"
            
            with open(mermaid_path, "w") as f:
                f.write(mermaid_source)
            
            print(f"   ✅ Mermaid source saved: {mermaid_path}")
            
        except Exception as e:
            print(f"   ❌ Error creating Mermaid: {e}")
        
        try:
            # Generate ASCII representation
            ascii_repr = conversation_graph.graph.get_graph().draw_ascii()
            ascii_path = output_dir / f"refactored_graph_{timestamp}.txt"
            
            with open(ascii_path, "w") as f:
                f.write(ascii_repr)
            
            print(f"   ✅ ASCII saved: {ascii_path}")
            
        except Exception as e:
            print(f"   ❌ Error creating ASCII: {e}")
        
        # Analyze the graph structure
        print(f"\n🔍 Graph Analysis:")
        
        # Count different types of nodes
        main_flow_nodes = [n for n in graph_info.nodes.keys() if n.startswith("flow_")]
        system_nodes = [n for n in graph_info.nodes.keys() if n in ["__start__", "__end__"]]
        core_nodes = [n for n in graph_info.nodes.keys() if not (n.startswith("flow_") or n in ["__start__", "__end__"])]
        
        print(f"   • Main Flow Nodes: {len(main_flow_nodes)}")
        print(f"   • Core Nodes: {len(core_nodes)}")
        print(f"   • System Nodes: {len(system_nodes)}")
        
        # Check for edges to package subgraph
        package_edges = [e for e in graph_info.edges if 'flow_package_start' in str(e)]
        print(f"   • Edges involving flow_package_start: {len(package_edges)}")
        
        print(f"\n🎉 Refactored Package Subgraph Test Complete!")
        print(f"📁 Output directory: {output_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing refactored subgraph: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_refactored_package_subgraph()
    
    if success:
        print(f"\n✅ Refactoring test passed!")
        print(f"💡 The refactored implementation now includes:")
        print(f"   ✅ flow_package_start as compiled subgraph")
        print(f"   ✅ State adapter pattern for compatibility")
        print(f"   ✅ Unified GraphState for all flows")
        print(f"   ✅ Complete LangGraph Studio visualization")
    else:
        print(f"\n❌ Refactoring test failed!")
