"""
Test script for the medical package subgraph implementation
"""

import asyncio
from app.flows.package_flow import flow_package_start
from app.models.schemas import ConversationState
from datetime import datetime

async def test_package_subgraph():
    """Test the medical package subgraph with various inputs"""

    test_cases = [
        {
            "input": "I'm looking for a botox package",
            "description": "Category search - aesthetic/botox"
        },
        {
            "input": "Do you have any vaccines available?",
            "description": "Category search - vaccines"
        },
        {
            "input": "I need package PK-VAC-HFMD-EV71-001",
            "description": "Specific package ID search (valid ID)"
        },
        {
            "input": "What health checkup packages do you have?",
            "description": "Category search - health checkup"
        },
        {
            "input": "I want something under 20000 baht",
            "description": "Price range search"
        },
        {
            "input": "Show me breast augmentation options",
            "description": "Specific aesthetic procedure search"
        },
        {
            "input": "I need a premium health check",
            "description": "Specific health package search"
        }
    ]

    print("🧪 Testing Medical Package Subgraph Implementation...")
    print("=" * 60)

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}: {test_case['description']}")
        print(f"   Input: '{test_case['input']}'")

        # Create conversation state
        state = ConversationState(
            session_id=f"test_package_{i}",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        try:
            # Run the package flow
            result = await flow_package_start(state, test_case["input"])

            print(f"   ✅ Response: {result['response'][:150]}...")
            print(f"   ✅ Completed: {result['completed']}")
            print(f"   ✅ Next Action: {result['next_action']}")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()

    print("\n" + "=" * 60)
    print("🎉 Medical package subgraph testing completed!")

if __name__ == "__main__":
    asyncio.run(test_package_subgraph())
